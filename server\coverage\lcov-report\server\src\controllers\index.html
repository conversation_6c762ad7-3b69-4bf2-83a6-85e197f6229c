
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for server/src/controllers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> server/src/controllers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.42% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>222/2634</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.92% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/1518</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.9% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/222</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.53% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>222/2601</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="apiV1Controller.js"><a href="apiV1Controller.js.html">apiV1Controller.js</a></td>
	<td data-value="6.08" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.08" class="pct low">6.08%</td>
	<td data-value="263" class="abs low">16/263</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="192" class="abs low">0/192</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="6.17" class="pct low">6.17%</td>
	<td data-value="259" class="abs low">16/259</td>
	</tr>

<tr>
	<td class="file low" data-value="authController.js"><a href="authController.js.html">authController.js</a></td>
	<td data-value="12.43" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.43" class="pct low">12.43%</td>
	<td data-value="185" class="abs low">23/185</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="78" class="abs low">0/78</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="12.43" class="pct low">12.43%</td>
	<td data-value="185" class="abs low">23/185</td>
	</tr>

<tr>
	<td class="file low" data-value="cartController.js"><a href="cartController.js.html">cartController.js</a></td>
	<td data-value="17.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.85" class="pct low">17.85%</td>
	<td data-value="56" class="abs low">10/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="17.85" class="pct low">17.85%</td>
	<td data-value="56" class="abs low">10/56</td>
	</tr>

<tr>
	<td class="file low" data-value="centralWalletController.js"><a href="centralWalletController.js.html">centralWalletController.js</a></td>
	<td data-value="7.97" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.97" class="pct low">7.97%</td>
	<td data-value="138" class="abs low">11/138</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="7.97" class="pct low">7.97%</td>
	<td data-value="138" class="abs low">11/138</td>
	</tr>

<tr>
	<td class="file low" data-value="countryController.js"><a href="countryController.js.html">countryController.js</a></td>
	<td data-value="21.73" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 21%"></div><div class="cover-empty" style="width: 79%"></div></div>
	</td>
	<td data-value="21.73" class="pct low">21.73%</td>
	<td data-value="23" class="abs low">5/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="22.72" class="pct low">22.72%</td>
	<td data-value="22" class="abs low">5/22</td>
	</tr>

<tr>
	<td class="file low" data-value="esimPlanController.js"><a href="esimPlanController.js.html">esimPlanController.js</a></td>
	<td data-value="4.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.33" class="pct low">4.33%</td>
	<td data-value="831" class="abs low">36/831</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="506" class="abs low">0/506</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="4.41" class="pct low">4.41%</td>
	<td data-value="816" class="abs low">36/816</td>
	</tr>

<tr>
	<td class="file low" data-value="esimStockController.js"><a href="esimStockController.js.html">esimStockController.js</a></td>
	<td data-value="12" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12" class="pct low">12%</td>
	<td data-value="100" class="abs low">12/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="34" class="abs low">0/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="12.24" class="pct low">12.24%</td>
	<td data-value="98" class="abs low">12/98</td>
	</tr>

<tr>
	<td class="file low" data-value="knowledgeBaseController.js"><a href="knowledgeBaseController.js.html">knowledgeBaseController.js</a></td>
	<td data-value="10" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="80" class="abs low">8/80</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="80" class="abs low">8/80</td>
	</tr>

<tr>
	<td class="file low" data-value="orderController.js"><a href="orderController.js.html">orderController.js</a></td>
	<td data-value="10.46" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.46" class="pct low">10.46%</td>
	<td data-value="583" class="abs low">61/583</td>
	<td data-value="3.52" class="pct low">3.52%</td>
	<td data-value="397" class="abs low">14/397</td>
	<td data-value="4.65" class="pct low">4.65%</td>
	<td data-value="43" class="abs low">2/43</td>
	<td data-value="10.64" class="pct low">10.64%</td>
	<td data-value="573" class="abs low">61/573</td>
	</tr>

<tr>
	<td class="file low" data-value="partnerApiController.js"><a href="partnerApiController.js.html">partnerApiController.js</a></td>
	<td data-value="12.19" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.19" class="pct low">12.19%</td>
	<td data-value="41" class="abs low">5/41</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="12.19" class="pct low">12.19%</td>
	<td data-value="41" class="abs low">5/41</td>
	</tr>

<tr>
	<td class="file low" data-value="partnerController.js"><a href="partnerController.js.html">partnerController.js</a></td>
	<td data-value="11" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11" class="pct low">11%</td>
	<td data-value="109" class="abs low">12/109</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="75" class="abs low">0/75</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="108" class="abs low">12/108</td>
	</tr>

<tr>
	<td class="file low" data-value="staffController.js"><a href="staffController.js.html">staffController.js</a></td>
	<td data-value="15.94" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 15%"></div><div class="cover-empty" style="width: 85%"></div></div>
	</td>
	<td data-value="15.94" class="pct low">15.94%</td>
	<td data-value="69" class="abs low">11/69</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="15.94" class="pct low">15.94%</td>
	<td data-value="69" class="abs low">11/69</td>
	</tr>

<tr>
	<td class="file low" data-value="walletController.js"><a href="walletController.js.html">walletController.js</a></td>
	<td data-value="7.69" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="156" class="abs low">12/156</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="156" class="abs low">12/156</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T10:51:55.228Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    