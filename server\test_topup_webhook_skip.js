/**
 * Manual test to verify that N009 webhooks are correctly skipped for topup orders
 * This test demonstrates the new functionality where topup orders don't trigger
 * N009 webhook processing since they don't generate new ICCID and QR codes.
 */

const orderController = require('./src/controllers/orderController');

// Mock console.log to capture output
const originalConsoleLog = console.log;
const logOutput = [];
console.log = (...args) => {
    logOutput.push(args.join(' '));
    originalConsoleLog(...args);
};

// Test function to simulate webhook processing
async function testTopupWebhookSkip() {
    console.log('🧪 Testing N009 webhook skip for topup orders...\n');

    // Mock database transaction
    const mockTransaction = {
        commit: () => {},
        rollback: () => {}
    };

    // Mock sequelize
    const sequelize = require('./src/config/database');
    const originalTransaction = sequelize.transaction;
    sequelize.transaction = () => mockTransaction;

    // Mock models
    const models = require('./src/models');
    const originalOrderFindOne = models.Order.findOne;
    
    // Test Case 1: Regular order (should process)
    console.log('📋 Test Case 1: Regular Order');
    models.Order.findOne = () => Promise.resolve({
        id: 'VLZ2030001',
        externalOrderId: 'BC_ORDER_123',
        providerMetadata: {}, // No rechargeOrder flag
        status: 'pending'
    });

    const regularOrderReq = {
        body: {
            tradeType: 'N009',
            tradeTime: '2024-01-15 10:30:00',
            tradeData: {
                orderId: 'BC_ORDER_123',
                channelOrderId: 'VLZ2030001',
                subOrderList: [{
                    iccid: '8988228045500123456',
                    qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                    apn: 'internet'
                }]
            }
        }
    };

    const regularOrderRes = {
        json: (data) => {
            console.log('✅ Regular order response:', JSON.stringify(data, null, 2));
            return regularOrderRes;
        }
    };

    try {
        await orderController.handleBillionConnectWebhook(regularOrderReq, regularOrderRes);
    } catch (error) {
        console.log('⚠️  Regular order processing encountered error (expected due to mocking):', error.message);
    }

    console.log('\n📋 Test Case 2: Topup Order');
    
    // Test Case 2: Topup order (should skip)
    models.Order.findOne = () => Promise.resolve({
        id: 'VLZ2030002',
        externalOrderId: 'BC_TOPUP_456',
        providerMetadata: {
            iccid: '8988228045500123456',
            rechargeOrder: true // This marks it as a topup order
        },
        status: 'pending'
    });

    const topupOrderReq = {
        body: {
            tradeType: 'N009',
            tradeTime: '2024-01-15 10:30:00',
            tradeData: {
                orderId: 'BC_TOPUP_456',
                channelOrderId: 'VLZ2030002',
                subOrderList: [{
                    iccid: '8988228045500123456',
                    qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                    apn: 'internet'
                }]
            }
        }
    };

    const topupOrderRes = {
        json: (data) => {
            console.log('✅ Topup order response:', JSON.stringify(data, null, 2));
            return topupOrderRes;
        }
    };

    try {
        await orderController.handleBillionConnectWebhook(topupOrderReq, topupOrderRes);
    } catch (error) {
        console.log('❌ Topup order processing failed:', error.message);
    }

    // Test Case 3: Non-N009 webhook (should ignore)
    console.log('\n📋 Test Case 3: Non-N009 Webhook');
    
    const nonN009Req = {
        body: {
            tradeType: 'N001',
            tradeTime: '2024-01-15 10:30:00',
            tradeData: {
                orderId: 'BC_ORDER_789'
            }
        }
    };

    const nonN009Res = {
        json: (data) => {
            console.log('✅ Non-N009 webhook response:', JSON.stringify(data, null, 2));
            return nonN009Res;
        }
    };

    try {
        await orderController.handleBillionConnectWebhook(nonN009Req, nonN009Res);
    } catch (error) {
        console.log('❌ Non-N009 webhook processing failed:', error.message);
    }

    // Restore original functions
    sequelize.transaction = originalTransaction;
    models.Order.findOne = originalOrderFindOne;
    console.log = originalConsoleLog;

    console.log('\n🎯 Test Summary:');
    console.log('- Regular orders: Should attempt processing (may fail due to mocking)');
    console.log('- Topup orders: Should skip processing with specific message');
    console.log('- Non-N009 webhooks: Should be ignored with generic message');
    console.log('\n✅ Test completed! Check the responses above to verify behavior.');
}

// Run the test
if (require.main === module) {
    testTopupWebhookSkip().catch(console.error);
}

module.exports = { testTopupWebhookSkip };
