name: Deploy to EC2

on:
  push:
    branches: [ main ]
    paths:
      - 'server/**'
      - 'Dockerfile'
      - 'Dockerfile.nginx'
      - 'docker-compose.yml'
      - 'deploy-script.sh'
      - 'setup-ssl.sh'
      - '.github/workflows/deploy.yml'
      - 'nginx/**'
      - 'ec2-setup.sh'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: server/package-lock.json

    - name: Install dependencies
      run: cd server && npm ci

    # - name: Run tests
    #   run: cd server && npm test

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Create .env file
      run: |
        cat > server/.env << EOL
        PORT=${{ secrets.PORT }}
        NODE_ENV=production
        CLIENT_URL=${{ secrets.CLIENT_URL }}
        DB_HOST=${{ secrets.DB_HOST }}
        DB_USER=${{ secrets.DB_USER }}
        DB_PASSWORD=${{ secrets.DB_PASSWORD }}
        DB_NAME=${{ secrets.DB_NAME }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}
        JWT_EXPIRES_IN=${{ secrets.JWT_EXPIRES_IN }}
        JWT_REFRESH_EXPIRES_IN=${{ secrets.JWT_REFRESH_EXPIRES_IN }}
        ZEPTO_API_URL=${{ secrets.ZEPTO_API_URL }}
        ZEPTO_API_KEY=${{ secrets.ZEPTO_API_KEY }}
        ZEPTO_FROM_EMAIL=${{ secrets.ZEPTO_FROM_EMAIL }}
        ZEPTO_FROM_EMAIL_NAME=${{ secrets.ZEPTO_FROM_EMAIL_NAME }}
        MAX_LOGIN_ATTEMPTS=${{ secrets.MAX_LOGIN_ATTEMPTS }}
        LOGIN_TIMEOUT=${{ secrets.LOGIN_TIMEOUT }}
        MOBIMATTER_API_URL=${{ secrets.MOBIMATTER_API_URL }}
        MOBIMATTER_API_KEY=${{ secrets.MOBIMATTER_API_KEY }}
        MOBIMATTER_MERCHANT_ID=${{ secrets.MOBIMATTER_MERCHANT_ID }}
        BILLIONCONNECT_API_URL=${{ secrets.BILLIONCONNECT_API_URL }}
        BILLIONCONNECT_CHANNEL_ID=${{ secrets.BILLIONCONNECT_CHANNEL_ID }}
        BILLIONCONNECT_APP_SECRET=${{ secrets.BILLIONCONNECT_APP_SECRET }}
        API_BASE_URL=${{ secrets.API_BASE_URL }}
        DEV_API_URL=${{ secrets.DEV_API_URL }}
        PROD_API_URL=${{ secrets.PROD_API_URL }}
        EOL

    - name: Deploy to EC2
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USERNAME }}
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          # Navigate to project directory (create if it doesn't exist)
          mkdir -p ~/esim-project
          cd ~/esim-project

          # Stop and remove existing containers
          if [ -f docker-compose.yml ]; then
            docker-compose down
          fi

          # Clean up old files (handle SSL certificate permissions)
          rm -rf server Dockerfile docker-compose.yml .env || true

          # Preserve SSL certificates but clean up other nginx files
          if [ -d "nginx" ]; then
            # Backup SSL certificates if they exist
            if [ -d "nginx/certbot/conf/live" ]; then
              echo "Backing up SSL certificates..."
              cp -r nginx/certbot/conf ssl_backup || true
            fi

            # Remove nginx directory
            rm -rf nginx || true

            # Restore SSL certificates if backup exists
            if [ -d "ssl_backup" ]; then
              echo "Restoring SSL certificates..."
              mkdir -p nginx/certbot/conf
              cp -r ssl_backup/* nginx/certbot/conf/ || true
              rm -rf ssl_backup
            fi
          fi

          # Create deployment directory
          mkdir -p server nginx/conf nginx/certbot/conf nginx/certbot/www

          # Create deployment script
          cat > deploy.sh << 'EOL'
          #!/bin/bash

          # Pull the latest code
          echo "Pulling latest code..."

          # Verify .env file exists
          echo "Setting up environment variables..."
          if [ -f "server/.env" ]; then
            echo ".env file found in server directory"
          else
            echo "Error: .env file not found in server directory"
            exit 1
          fi

          # Start the containers
          echo "Starting containers..."
          docker-compose up -d

          echo "Deployment completed successfully!"
          EOL

          # Make the script executable
          chmod +x deploy.sh

          # Create Nginx configuration
          echo "Creating Nginx configuration..."

    - name: Copy files to EC2
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USERNAME }}
        key: ${{ secrets.EC2_SSH_KEY }}
        source: "server/,Dockerfile,Dockerfile.nginx,docker-compose.yml,deploy-script.sh,setup-ssl.sh"
        target: "~/esim-project"
        strip_components: 0

    - name: Deploy application
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USERNAME }}
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          cd ~/esim-project

          # Create required directories
          mkdir -p nginx/conf nginx/certbot/conf nginx/certbot/www

          # Make scripts executable
          chmod +x deploy-script.sh
          chmod +x setup-ssl.sh

          # Run deployment script with environment variables
          DOMAIN_NAME="${{ secrets.DOMAIN_NAME }}" ADMIN_EMAIL="${{ secrets.ADMIN_EMAIL }}" ./deploy-script.sh
