# BillionConnect F046 Usage Information Implementation

## Overview

This document describes the implementation of BillionConnect's F046 API for querying data plan usage information. The implementation allows the eSIM platform to retrieve real-time usage data for BillionConnect orders.

## API Specification

### F046 - Query Data Plan Usage Information

**Purpose**: Query usage information of ICCID packages through the BillionConnect API.

**Note**: Data usage cannot be queried if planEndTime exceeds 180 days.

### Request Format

```json
{
    "tradeType": "F046",
    "tradeTime": "2023-05-30 15:53:32",
    "tradeData": {
        "orderId": "2684910712887645",
        "channelOrderId": "71186072946800",
        "iccid": "89860012018880000641",
        "language": "2"
    }
}
```

### Response Format

```json
{
    "tradeCode": "1000",
    "tradeMsg": "Success",
    "tradeData": {
        "orderId": "2684910712887645",
        "channelOrderId": "71186072946800",
        "subOrderList": [
            {
                "subOrderId": "1684910712888646",
                "channelSubOrderId": "21186072505668",
                "skuId": "1683599122978282",
                "skuName": "Japan-4G-300MB/day+tonguxnzaiti",
                "copies": "2",
                "planStatus": "2",
                "planStartTime": "2023-05-24 14:59:36",
                "planEndTime": "2023-05-26 14:59:37",
                "totalDays": "2",
                "totalTraffic": "-1",
                "usageInfoList": [
                    {
                        "usedDate": "20230524",
                        "usageAmt": "10240"
                    }
                ],
                "highFlowSize": "307200",
                "planType": "1"
            }
        ]
    }
}
```

## Implementation Details

### 1. Service Layer (`billionconnect.service.js`)

#### New Method: `getUsageInfo(orderId, channelOrderId, iccid, language)`

**Parameters:**
- `orderId` (string): BillionConnect order ID
- `channelOrderId` (string, optional): Channel order ID
- `iccid` (string, required): ICCID to query usage for
- `language` (string, optional): Language code (1=Chinese, 2=English, default: 2)

**Returns:** Standardized usage data object

#### New Helper Method: `_parseUsageData(subOrder)`

Parses the raw BillionConnect response into standardized format:

**Plan Status Mapping:**
- `0`: Not Available (not used)
- `1`: Active (in use)
- `2`: Expired (used)
- `3`: Cancelled

**Data Calculations:**
- **Usage**: Sum of all daily usage amounts (converted from KB to bytes)
- **Allowance**: 
  - For unlimited plans (`totalTraffic = -1`): `null`
  - For daily plans (`planType = 1`): `highFlowSize * totalDays * 1024`
  - For total plans (`planType = 0`): `totalTraffic * 1024`

### 2. Controller Updates

#### Order Controller (`orderController.js`)

Updated `getOrderUsage()` method to support BillionConnect:
- Checks for both Mobimatter and BillionConnect providers
- Extracts ICCID from multiple sources (stock, metadata, response)
- Calls appropriate service method with required parameters

#### API v1 Controller (`apiV1Controller.js`)

Updated `getUsageInfo()` method:
- Enhanced provider detection
- Added BillionConnect-specific parameter handling
- Maintains backward compatibility with Mobimatter

### 3. Data Flow

```
1. Frontend/API Request → Controller
2. Controller → Validates order and provider
3. Controller → Extracts ICCID and order IDs
4. Controller → Calls BillionConnect service
5. Service → Makes F046 API request
6. Service → Parses response data
7. Service → Returns standardized format
8. Controller → Updates database
9. Controller → Returns response to client
```

## Usage Examples

### Web Interface
```javascript
// GET /api/orders/:orderId/usage
// Automatically detects provider and handles accordingly
```

### API v1
```javascript
// GET /api/v1/usage/:orderId
// Returns standardized usage data for any supported provider
```

## Error Handling

The implementation includes comprehensive error handling:

1. **Missing ICCID**: Returns error if ICCID cannot be found
2. **API Errors**: Handles BillionConnect API error responses
3. **Data Parsing**: Graceful handling of malformed response data
4. **Network Issues**: Proper error propagation and logging

## Testing

A comprehensive test suite is included (`test_billionconnect_usage.js`) that verifies:

- ✅ Data usage calculation (KB to bytes conversion)
- ✅ Data allowance calculation (daily vs total plans)
- ✅ Status determination logic
- ✅ Plan status mapping
- ✅ Unlimited plan handling
- ✅ Date parsing and expiry detection

## Database Schema

The implementation uses existing Order model fields:

- `usageData` (JSON): Complete usage response
- `dataUsage` (BIGINT): Total usage in bytes
- `dataAllowance` (BIGINT): Total allowance in bytes
- `usageStatus` (ENUM): Standardized status
- `expiryDate` (DATE): Plan expiry date
- `usageMessage` (TEXT): Additional usage information
- `lastUsageCheck` (DATE): Last update timestamp

## Integration Points

### Frontend Integration
- Existing usage components work without modification
- **Automatic usage data fetching**: BillionConnect orders now automatically fetch usage data when navigating to order details (fixed frontend condition)
- Real-time data fetching for BillionConnect orders
- Consistent UI across all providers
- Top-up functionality available for both Mobimatter and BillionConnect orders

### API Integration
- Maintains existing API contract
- Backward compatible with Mobimatter implementation
- Standardized response format

## Limitations

1. **180-Day Limit**: Cannot query usage for plans expired over 180 days
2. **ICCID Required**: Must have ICCID available for usage queries
3. **Single ICCID**: Current implementation queries one ICCID at a time

## Bug Fixes

### Automatic Usage Data Loading Issue
**Problem**: BillionConnect orders required manual refresh to see usage data, while Mobimatter orders loaded automatically.

**Root Cause**: Frontend OrderDetails component only checked for 'Mobimatter' provider when deciding to automatically fetch usage data.

**Solution**: Updated the condition in `client/src/pages/partner/OrderDetails.jsx` to include both providers:
```javascript
// Before
if (response.data.plan?.provider?.name === 'Mobimatter') {
    fetchUsageData();
}

// After
const providerName = response.data.plan?.provider?.name;
if (providerName === 'Mobimatter' || providerName === 'Billionconnect') {
    fetchUsageData();
}
```

**Result**: BillionConnect orders now automatically load usage data when navigating to order details page, providing consistent user experience across all providers.

## Future Enhancements

1. **Batch Queries**: Support multiple ICCIDs in single request
2. **Caching**: Implement usage data caching to reduce API calls
3. **Webhooks**: Real-time usage updates via webhooks
4. **Historical Data**: Store and display usage history trends
