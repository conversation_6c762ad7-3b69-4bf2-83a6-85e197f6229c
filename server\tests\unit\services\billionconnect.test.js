// Mock the axios module
jest.mock('axios');
const axios = require('axios');

// Set up environment variables before requiring the service
process.env.BILLIONCONNECT_API_URL = 'https://test-api.billionconnect.com';
process.env.BILLIONCONNECT_CHANNEL_ID = 'test-channel-id';
process.env.BILLIONCONNECT_APP_SECRET = 'test-app-secret';

const billionconnectService = require('../../../src/services/billionconnect.service');

describe('BillionConnect Service', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('queryRechargeCommodities', () => {
        it('should successfully query recharge commodities', async () => {
            const mockResponse = {
                data: {
                    tradeCode: '1000',
                    tradeMsg: 'Success',
                    tradeData: {
                        skuId: ['132342909036', '132346328450']
                    }
                }
            };

            axios.post.mockResolvedValue(mockResponse);

            const result = await billionconnectService.queryRechargeCommodities('89812003916820397415');

            expect(result).toEqual(['132342909036', '132346328450']);
            expect(axios.post).toHaveBeenCalledWith(
                'https://test-api.billionconnect.com',
                expect.objectContaining({
                    tradeType: 'F052',
                    tradeData: {
                        iccid: '89812003916820397415'
                    }
                }),
                expect.objectContaining({
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json;charset=UTF-8',
                        'x-channel-id': 'test-channel-id'
                    })
                })
            );
        });

        it('should handle API errors', async () => {
            const mockResponse = {
                data: {
                    tradeCode: '1001',
                    tradeMsg: 'ICCID not found'
                }
            };

            axios.post.mockResolvedValue(mockResponse);

            await expect(billionconnectService.queryRechargeCommodities('invalid-iccid'))
                .rejects.toThrow('Failed to query recharge commodities: ICCID not found');
        });
    });

    describe('createRechargeOrder', () => {
        it('should successfully create a recharge order', async () => {
            const mockResponse = {
                data: {
                    tradeCode: '1000',
                    tradeMsg: 'Success',
                    tradeData: {
                        orderId: '13131313131',
                        channelOrderId: '138788765467',
                        subOrderList: [
                            {
                                subOrderId: '13131313132',
                                channelSubOrderId: '138788765467_sub'
                            }
                        ]
                    }
                }
            };

            axios.post.mockResolvedValue(mockResponse);

            const orderData = {
                channelOrderId: '138788765467',
                iccidArray: ['89860012017300000001'],
                skuId: '132342909036',
                copies: 1,
                totalAmount: '50.00',
                comment: 'Test top-up order'
            };

            const result = await billionconnectService.createRechargeOrder(orderData);

            expect(result).toEqual({
                orderId: '13131313131',
                channelOrderId: '138788765467',
                subOrderList: [
                    {
                        subOrderId: '13131313132',
                        channelSubOrderId: '138788765467_sub'
                    }
                ],
                providerResponse: mockResponse.data
            });

            expect(axios.post).toHaveBeenCalledWith(
                'https://test-api.billionconnect.com',
                expect.objectContaining({
                    tradeType: 'F007',
                    tradeData: expect.objectContaining({
                        channelOrderId: '138788765467',
                        subOrderList: expect.arrayContaining([
                            expect.objectContaining({
                                channelSubOrderId: '138788765467_sub',
                                iccid: ['89860012017300000001'],
                                skuId: '132342909036',
                                copies: '1'
                            })
                        ])
                    })
                }),
                expect.objectContaining({
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json;charset=UTF-8',
                        'x-channel-id': 'test-channel-id'
                    })
                })
            );
        });

        it('should validate required fields', async () => {
            const invalidOrderData = {
                channelOrderId: '138788765467',
                // Missing iccidArray and skuId
            };

            await expect(billionconnectService.createRechargeOrder(invalidOrderData))
                .rejects.toThrow('channelOrderId, iccidArray, and skuId are required for recharge order');
        });

        it('should validate ICCID array size', async () => {
            const orderData = {
                channelOrderId: '138788765467',
                iccidArray: new Array(501).fill('89860012017300000001'), // 501 items (exceeds limit)
                skuId: '132342909036'
            };

            await expect(billionconnectService.createRechargeOrder(orderData))
                .rejects.toThrow('ICCID array cannot exceed 500 items');
        });

        it('should handle API errors', async () => {
            const mockResponse = {
                data: {
                    tradeCode: '1002',
                    tradeMsg: 'Invalid SKU ID'
                }
            };

            axios.post.mockResolvedValue(mockResponse);

            const orderData = {
                channelOrderId: '138788765467',
                iccidArray: ['89860012017300000001'],
                skuId: 'invalid-sku'
            };

            await expect(billionconnectService.createRechargeOrder(orderData))
                .rejects.toThrow('Failed to create recharge order: Invalid SKU ID');
        });
    });
});
