name: Deploy with Database Optimization

on:
  # Manual trigger only - comment out automatic triggers if desired
  # push:
  #   branches:
  #     - main                    # Production releases
  #     - release/*              # Release branches
  #     - feature/performance-*  # Performance-related features
  #   paths-ignore:
  #     - 'README.md'
  #     - 'docs/**'
  #     - '.gitignore'
  #     - 'LICENSE'

  # Scheduled optimization deployment (weekly maintenance) - optional
  # schedule:
  #   - cron: '0 2 * * 1'        # Every Monday at 2 AM UTC

  # Manual trigger with environment selection
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production
      optimization_level:
        description: 'Optimization level'
        required: true
        default: 'full'
        type: choice
        options:
          - full
          - minimal
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean
      run_optimization:
        description: 'Run database optimization'
        required: false
        default: true
        type: boolean

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: server/package-lock.json

    - name: Install dependencies
      run: cd server && npm ci

    # - name: Run tests (uncomment if needed)
    #   run: cd server && npm test
    #   if: ${{ github.event.inputs.skip_tests != 'true' }}

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Create .env file
      run: |
        cat > server/.env << EOL
        PORT=${{ secrets.PORT }}
        NODE_ENV=production
        CLIENT_URL=${{ secrets.CLIENT_URL }}
        DB_HOST=${{ secrets.DB_HOST }}
        DB_USER=${{ secrets.DB_USER }}
        DB_PASSWORD=${{ secrets.DB_PASSWORD }}
        DB_NAME=${{ secrets.DB_NAME }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}
        JWT_EXPIRES_IN=${{ secrets.JWT_EXPIRES_IN }}
        JWT_REFRESH_EXPIRES_IN=${{ secrets.JWT_REFRESH_EXPIRES_IN }}
        ZEPTO_API_URL=${{ secrets.ZEPTO_API_URL }}
        ZEPTO_API_KEY=${{ secrets.ZEPTO_API_KEY }}
        ZEPTO_FROM_EMAIL=${{ secrets.ZEPTO_FROM_EMAIL }}
        ZEPTO_FROM_EMAIL_NAME=${{ secrets.ZEPTO_FROM_EMAIL_NAME }}
        MAX_LOGIN_ATTEMPTS=${{ secrets.MAX_LOGIN_ATTEMPTS }}
        LOGIN_TIMEOUT=${{ secrets.LOGIN_TIMEOUT }}
        MOBIMATTER_API_URL=${{ secrets.MOBIMATTER_API_URL }}
        MOBIMATTER_API_KEY=${{ secrets.MOBIMATTER_API_KEY }}
        MOBIMATTER_MERCHANT_ID=${{ secrets.MOBIMATTER_MERCHANT_ID }}
        BILLIONCONNECT_API_URL=${{ secrets.BILLIONCONNECT_API_URL }}
        BILLIONCONNECT_CHANNEL_ID=${{ secrets.BILLIONCONNECT_CHANNEL_ID }}
        BILLIONCONNECT_APP_SECRET=${{ secrets.BILLIONCONNECT_APP_SECRET }}
        API_BASE_URL=${{ secrets.API_BASE_URL }}
        DEV_API_URL=${{ secrets.DEV_API_URL }}
        PROD_API_URL=${{ secrets.PROD_API_URL }}
        EOL

    - name: Copy files to EC2
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USERNAME }}
        key: ${{ secrets.EC2_SSH_KEY }}
        source: "server/,Dockerfile,Dockerfile.nginx,docker-compose.yml,deploy-script.sh,setup-ssl.sh"
        target: "~/esim-project"
        strip_components: 0

    - name: Deploy to EC2
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USERNAME }}
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          cd ~/esim-project

          # Create required directories
          mkdir -p nginx/conf nginx/certbot/conf nginx/certbot/www

          # Make scripts executable
          chmod +x deploy-script.sh
          chmod +x setup-ssl.sh

          # Run deployment script with environment variables
          DOMAIN_NAME="${{ secrets.DOMAIN_NAME }}" ADMIN_EMAIL="${{ secrets.ADMIN_EMAIL }}" ./deploy-script.sh

          # Run database optimization based on conditions
          SHOULD_OPTIMIZE="false"

          # Check if manual trigger with optimization enabled
          if [ "${{ github.event.inputs.run_optimization }}" = "true" ]; then
            SHOULD_OPTIMIZE="true"
          fi

          # Auto-optimize for main branch pushes (production)
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            SHOULD_OPTIMIZE="true"
          fi

          # Auto-optimize for scheduled runs
          if [ "${{ github.event_name }}" = "schedule" ]; then
            SHOULD_OPTIMIZE="true"
          fi

          # Auto-optimize for release branches
          if [[ "${{ github.ref }}" == refs/heads/release/* ]]; then
            SHOULD_OPTIMIZE="true"
          fi

          if [ "$SHOULD_OPTIMIZE" = "true" ]; then
            echo "🚀 Running database optimization..."

            # Navigate to server directory for optimization
            cd server

            # Check optimization level
            if [ "${{ github.event.inputs.optimization_level }}" = "minimal" ]; then
              echo "Running minimal optimization..."
              npm run optimize-db:minimal || echo "Minimal optimization script not found, running full optimization"
              npm run optimize-db || echo "Optimization completed with warnings"
            else
              echo "Running full optimization..."
              npm run optimize-db || echo "Optimization completed with warnings"
            fi

            # Return to project root
            cd ..
          else
            echo "⏭️  Skipping database optimization for this deployment"
          fi

          # Check if containers are running and restart if needed
          echo "Checking container status..."
          if [ $(docker ps -q | wc -l) -lt 3 ]; then
            echo "Some containers are not running. Restarting..."
            docker-compose down
            docker-compose up -d
          else
            echo "✅ All containers are running"
          fi

          # Final health check
          echo "Performing final health check..."
          sleep 5

          # Check if app container is healthy
          if docker exec esim-backend curl -f http://localhost:3000/ > /dev/null 2>&1; then
            echo "✅ Application is responding"
          else
            echo "⚠️  Application health check failed, checking logs..."
            docker-compose logs app
          fi

          echo "✅ Deployment with optimization completed successfully!"
          echo "🌐 Environment: ${{ github.event.inputs.environment || 'production' }}"
          echo "📊 Optimization: $SHOULD_OPTIMIZE"
          echo "🔧 Optimization Level: ${{ github.event.inputs.optimization_level || 'full' }}"
