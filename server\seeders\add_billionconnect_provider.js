const { v4: uuidv4 } = require('uuid');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.bulkInsert('Providers', [{
            id: uuidv4(),
            name: 'billionconnect',
            type: 'API',
            country: 'Global',
            apiEndpoint: process.env.BILLIONCONNECT_API_URL || 'https://api-flow-ts.billionconnect.com/Flow/saler/2.0/invoke',
            apiKey: process.env.BILLIONCONNECT_CHANNEL_ID,
            status: 'active',
            description: 'BillionConnect eSIM Provider - Global eSIM solutions with comprehensive coverage',
            configData: JSON.stringify({
                channelId: process.env.BILLIONCONNECT_CHANNEL_ID,
                appSecret: process.env.BILLIONCONNECT_APP_SECRET,
                supportedTradeTypes: ['F002', 'F003', 'F040', 'N009', 'F012', 'F007', 'F008', 'F011', 'F052'],
                features: {
                    getCommodities: true,
                    getPrices: true,
                    createOrder: true,
                    getQRCode: true,
                    getUsage: true,
                    recharge: true,
                    cancelOrder: true,
                    queryOrder: true
                }
            }),
            createdAt: new Date(),
            updatedAt: new Date()
        }], {});
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.bulkDelete('Providers', { name: 'billionconnect' }, {});
    }
};
