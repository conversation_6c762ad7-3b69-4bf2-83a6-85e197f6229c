const exchangeRateService = require('./src/services/exchangeRate.service');
const billionconnectService = require('./src/services/billionconnect.service');

/**
 * Test script to verify real-time exchange rate functionality
 */

async function testExchangeRateService() {
    console.log('🧪 Testing Real-time Exchange Rate Service...\n');

    try {
        // Test 1: Get exchange rate info
        console.log('Test 1: Getting exchange rate information');
        const rateInfo = await exchangeRateService.getExchangeRateInfo();
        console.log('Exchange Rate Info:', {
            currentRate: rateInfo.currentRate,
            isCached: rateInfo.isCached,
            cacheExpiry: rateInfo.cacheExpiry,
            fallbackRate: rateInfo.fallbackRate,
            availableAPIs: rateInfo.availableAPIs.map(api => ({
                name: api.name,
                keyConfigured: api.keyConfigured
            }))
        });
        console.log('✅ Exchange rate info retrieved\n');

        // Test 2: Get current CNY to USD rate
        console.log('Test 2: Getting current CNY to USD rate');
        const currentRate = await exchangeRateService.getCNYToUSDRate();
        console.log(`Current rate: 1 CNY = ${currentRate} USD`);
        console.log('✅ Current rate retrieved\n');

        // Test 3: Convert CNY amounts to USD
        console.log('Test 3: Converting CNY amounts to USD');
        const testAmounts = [10, 50, 100, 500];
        
        for (const cnyAmount of testAmounts) {
            const usdAmount = await exchangeRateService.convertCNYToUSD(cnyAmount);
            console.log(`${cnyAmount} CNY = $${usdAmount} USD`);
        }
        console.log('✅ Currency conversions completed\n');

        // Test 4: Test caching behavior
        console.log('Test 4: Testing cache behavior');
        console.log('Getting rate (should use cache):');
        const cachedRate = await exchangeRateService.getCNYToUSDRate();
        console.log(`Cached rate: 1 CNY = ${cachedRate} USD`);
        
        console.log('Forcing refresh:');
        const refreshedRate = await exchangeRateService.getCNYToUSDRate(true);
        console.log(`Refreshed rate: 1 CNY = ${refreshedRate} USD`);
        console.log('✅ Cache behavior tested\n');

        // Test 5: Test BillionConnect integration
        console.log('Test 5: Testing BillionConnect integration');
        
        // Mock price data for testing
        const mockPriceData = {
            skuId: 'TEST_SKU_001',
            price: [{
                copies: 1,
                settlementPrice: '35.50',
                retailPrice: '50.00'
            }, {
                copies: 10,
                settlementPrice: '32.00',
                retailPrice: '45.00'
            }]
        };

        console.log('Converting mock BillionConnect price data...');
        const transformedPrice = await billionconnectService.transformPriceData(mockPriceData, 1);
        
        if (transformedPrice) {
            console.log('Transformed price data:', {
                skuId: transformedPrice.skuId,
                buyingPrice: transformedPrice.buyingPrice,
                retailPrice: transformedPrice.retailPrice,
                currency: transformedPrice.currency,
                exchangeRate: transformedPrice.providerMetadata.exchangeRate,
                exchangeRateTimestamp: transformedPrice.providerMetadata.exchangeRateTimestamp
            });
            console.log('✅ BillionConnect integration working\n');
        } else {
            console.log('❌ BillionConnect integration failed\n');
        }

        // Test 6: Test error handling
        console.log('Test 6: Testing error handling');
        
        // Clear cache and test with no internet (simulated)
        exchangeRateService.clearCache();
        console.log('Cache cleared');
        
        // This should still work with fallback rate if APIs fail
        const fallbackRate = await exchangeRateService.getCNYToUSDRate();
        console.log(`Rate with potential API failures: 1 CNY = ${fallbackRate} USD`);
        console.log('✅ Error handling tested\n');

        // Test 7: Performance test
        console.log('Test 7: Performance test');
        const startTime = Date.now();
        
        // Multiple concurrent conversions
        const promises = [];
        for (let i = 0; i < 10; i++) {
            promises.push(exchangeRateService.convertCNYToUSD(100 + i));
        }
        
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        console.log(`Converted 10 amounts concurrently in ${endTime - startTime}ms`);
        console.log('Results:', results);
        console.log('✅ Performance test completed\n');

        console.log('🎉 All exchange rate tests completed successfully!');
        
        console.log('\n📋 Summary of Real-time Exchange Rate Features:');
        console.log('1. ✅ Multiple API providers with fallback');
        console.log('2. ✅ Intelligent caching (30-minute expiry)');
        console.log('3. ✅ Real-time CNY to USD conversion');
        console.log('4. ✅ BillionConnect service integration');
        console.log('5. ✅ Error handling with fallback rates');
        console.log('6. ✅ Performance optimized with concurrent requests');
        console.log('7. ✅ Admin API endpoints for monitoring and refresh');

    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Test individual API endpoints (if available)
async function testAPIEndpoints() {
    console.log('\n🌐 Testing individual API endpoints...\n');
    
    const apis = [
        {
            name: 'ExchangeRate-API',
            url: 'https://api.exchangerate-api.com/v4/latest/CNY',
            extractRate: (data) => data.rates?.USD
        }
    ];
    
    for (const api of apis) {
        try {
            console.log(`Testing ${api.name}...`);
            const axios = require('axios');
            const response = await axios.get(api.url, { timeout: 5000 });
            const rate = api.extractRate(response.data);
            
            if (rate) {
                console.log(`✅ ${api.name}: 1 CNY = ${rate} USD`);
            } else {
                console.log(`❌ ${api.name}: Invalid response format`);
            }
        } catch (error) {
            console.log(`❌ ${api.name}: ${error.message}`);
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    testExchangeRateService()
        .then(() => testAPIEndpoints())
        .then(() => {
            console.log('\n✨ All tests completed!');
            process.exit(0);
        })
        .catch(error => {
            console.error('Test suite failed:', error);
            process.exit(1);
        });
}

module.exports = { testExchangeRateService, testAPIEndpoints };
