const axios = require('axios');

const sendOTPEmail = async (email, otp) => {
    try {
        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: "Your OTP for eSIM Platform",
                textbody: `Your OTP is: ${otp}. This code will expire in 5 minutes.`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2>eSIM Platform Authentication</h2>
                        <p>Your One-Time Password (OTP) is:</p>
                        <h1 style="color: #4a90e2; font-size: 32px; letter-spacing: 5px;">${otp}</h1>
                        <p>This code will expire in 5 minutes.</p>
                        <p>If you didn't request this OTP, please ignore this email.</p>
                        <hr>
                        <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        return response.data;
    } catch (error) {
        console.error('Email sending failed:', error.response?.data || error.message);
        throw new Error('Failed to send OTP email');
    }
};

const sendPasswordResetEmail = async (email, resetLink) => {
    try {
        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: "Reset Your Password - eSIM Platform",
                textbody: `Click this link to reset your password: ${resetLink}. This link will expire in 1 hour.`,
                htmlbody: `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                    </head>
                    <body>
                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                            <tr>
                                <td align="center">
                                    <table width="600" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial, sans-serif;">
                                        <tr>
                                            <td align="center" style="padding: 40px 0 30px;">
                                                <h2 style="color: #333; margin: 0;">eSIM Platform Password Reset</h2>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" style="padding: 0 30px;">
                                                <p style="color: #666; font-size: 16px; line-height: 24px;">
                                                    You requested to reset your password. Click the button below to proceed:
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" style="padding: 30px 0;">
                                                <!-- Button for Outlook -->
                                                <!--[if mso]>
                                                <table cellspacing="0" cellpadding="0" border="0" align="center">
                                                    <tr>
                                                        <td align="center" bgcolor="#4a90e2" style="border-radius: 4px;">
                                                            <a href="${resetLink}" target="_blank" 
                                                               style="display: block; padding: 12px 20px; font-size: 16px; font-weight: bold; color: #ffffff; 
                                                                      text-decoration: none; background-color: #4a90e2; border-radius: 4px;">
                                                                Reset Password
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <![endif]-->

                                                <!-- Button for all other email clients -->
                                                <table cellspacing="0" cellpadding="0" border="0" align="center">
                                                    <tr>
                                                        <td align="center" bgcolor="#4a90e2" style="border-radius: 4px;">
                                                            <a href="${resetLink}" target="_blank"
                                                               style="display: inline-block; padding: 12px 20px; font-size: 16px; font-weight: bold; 
                                                                      color: #ffffff; text-decoration: none; background-color: #4a90e2; 
                                                                      border-radius: 4px;">
                                                                Reset Password
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" style="padding: 0 30px;">
                                                <p style="color: #666; font-size: 14px; line-height: 21px;">
                                                    This link will expire in 1 hour.<br>
                                                    If you didn't request this password reset, please ignore this email.
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 30px 30px 0; border-top: 1px solid #e6e6e6;">
                                                <p style="color: #666; font-size: 12px; margin: 0;">
                                                    This is an automated message, please do not reply.
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </body>
                    </html>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        return response.data;
    } catch (error) {
        console.error('Email sending failed:', error.response?.data || error.message);
        throw new Error('Failed to send password reset email');
    }
};

// Send low balance notification email
const sendLowBalanceEmail = async (email, currentBalance, threshold) => {
    try {
        if (!email) {
            console.error('Cannot send low balance email: No email address provided');
            return;
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: 'Low Wallet Balance Alert',
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #e11d48;">Low Balance Alert</h2>
                        <p>Your wallet balance has fallen below your set threshold.</p>
                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>Current Balance:</strong> $${currentBalance.toFixed(2)}</p>
                            <p style="margin: 5px 0;"><strong>Threshold:</strong> $${threshold.toFixed(2)}</p>
                        </div>
                        <p>Please top up your wallet to ensure uninterrupted service.</p>
                        <p style="color: #6b7280; font-size: 0.9em;">
                            You can adjust your balance threshold at any time in your wallet settings.
                        </p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        console.log(`Low balance notification sent to ${email}`);
        return response.data;
    } catch (error) {
        console.error('Error sending low balance email:', error.response?.data || error.message);
        // Don't throw the error as this is a notification service
        // We don't want to break the main transaction if email fails
    }
};

// Generic email sending function
const sendEmail = async (emailContent) => {
    try {
        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: emailContent.to
                    }
                }],
                subject: emailContent.subject,
                htmlbody: emailContent.html,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        return response.data;
    } catch (error) {
        console.error('Email sending failed:', error.response?.data || error.message);
        throw error;
    }
};

// Send wallet credit notification email
const sendWalletCreditEmail = async (email, amount, newBalance) => {
    if (!email) {
        console.error('Cannot send credit notification email: No email address provided');
        return;
    }

    const emailContent = {
        to: email,
        subject: 'Wallet Credit Notification',
        html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #16a34a;">Wallet Credit Notification</h2>
                <p>Your wallet has been credited with new funds.</p>
                <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <p style="margin: 5px 0;"><strong>Amount Credited:</strong> $${amount.toFixed(2)}</p>
                    <p style="margin: 5px 0; color: #16a34a;"><strong>New Balance:</strong> $${newBalance.toFixed(2)}</p>
                </div>
                <p>Thank you for using our service!</p>
                <hr>
                <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply.</p>
            </div>
        `
    };

    try {
        await sendEmail(emailContent);
        console.log(`Credit notification sent to ${email}`);
    } catch (error) {
        console.error('Failed to send credit notification:', error);
        // Don't throw error as this is a notification service
    }
};

// Send low stock notification email to admin
const sendLowStockEmail = async (adminEmails, planDetails, currentStock) => {
    try {
        if (!adminEmails || adminEmails.length === 0) {
            console.error('Cannot send low stock email: No admin emails provided');
            return;
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: adminEmails.map(email => ({
                    email_address: {
                        address: email
                    }
                })),
                subject: `Low Stock Alert: ${planDetails.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #e11d48;">Low Stock Alert</h2>
                        <p>The following eSIM plan is running low on stock:</p>
                        
                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Plan Details</h3>
                            <p style="margin: 5px 0;"><strong>Name:</strong> ${planDetails.name}</p>
                            <p style="margin: 5px 0;"><strong>Product ID:</strong> ${planDetails.productId}</p>
                            <p style="margin: 5px 0;"><strong>Current Stock:</strong> <span style="color: #e11d48;">${currentStock}</span></p>
                            <p style="margin: 5px 0;"><strong>Stock Threshold:</strong> ${planDetails.stockThreshold}</p>
                            <p style="margin: 5px 0;"><strong>Provider:</strong> ${planDetails.provider?.name}</p>
                            <p style="margin: 5px 0;"><strong>Network:</strong> ${planDetails.networkName}</p>
                            <p style="margin: 5px 0;"><strong>Validity:</strong> ${planDetails.validityDays} days</p>
                            <p style="margin: 5px 0;"><strong>Plan Type:</strong> ${planDetails.planType}</p>
                            ${planDetails.planType === 'Unlimited' ? 
                                `<p style="margin: 5px 0;"><strong>Data:</strong> Unlimited</p>` : 
                                planDetails.planType === 'Custom' ? 
                                `<p style="margin: 5px 0;"><strong>Data:</strong> ${planDetails.customPlanData}</p>`:
                                `<p style="margin: 5px 0;"><strong>Data:</strong> ${planDetails.planData} ${planDetails.planDataUnit}</p>`
                            }
                        </div>

                        <p style="color: #e11d48; font-weight: bold;">Please add more stock to ensure uninterrupted service.</p>
                        
                        <hr style="margin: 20px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        console.log(`Low stock notification sent to ${adminEmails.length} admins`);
        return response.data;
    } catch (error) {
        console.error('Failed to send low stock notification:', error.response?.data || error.message);
        // Don't throw error as this is a notification service
    }
};

// Send initial order confirmation email for BillionConnect orders (without eSIM details)
const sendBillionConnectInitialOrderEmail = async (email, orderDetails) => {
    try {
        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: `Order Confirmation - ${orderDetails.plan.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">Order Successfully Created!</h2>
                        <p>Thank you for your order. Your eSIM order has been successfully placed and is being processed.</p>

                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Order Summary</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666; width: 150px;"><strong>Order ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.order.id}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Plan Name</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.name}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Validity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.validityDays} days</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Data</strong></td>
                                    <td style="padding: 8px 0;">${
                                        orderDetails.plan.planType === 'Unlimited' ? 'Unlimited' :
                                        orderDetails.plan.planType === 'Custom' ? orderDetails.plan.customPlanData :
                                        `${orderDetails.plan.planData} ${orderDetails.plan.planDataUnit}`
                                    }</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Quantity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.quantity}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Order Total</strong></td>
                                    <td style="padding: 8px 0;">$${orderDetails.orderTotal}</td>
                                </tr>
                                ${orderDetails.startDate ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Start Date</strong></td>
                                    <td style="padding: 8px 0;">${new Date(orderDetails.startDate).toLocaleDateString()}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>

                        <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
                            <h3 style="margin: 0 0 10px; color: #1976d2;">What's Next?</h3>
                            <p style="margin: 0; color: #1976d2;">
                                Your eSIM is being prepared by our provider. You will receive another email shortly with:
                            </p>
                            <ul style="color: #1976d2; margin: 10px 0; padding-left: 20px;">
                                <li>Your eSIM activation details (ICCID, SMDP Address, etc.)</li>
                                <li>QR code for easy installation</li>
                                <li>Step-by-step installation instructions</li>
                            </ul>
                            <p style="margin: 10px 0 0; color: #1976d2; font-weight: bold;">
                                This usually takes just a few minutes.
                            </p>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">Important:</h4>
                            <ul style="color: #856404; padding-left: 20px;">
                                <li>Keep this email for your records</li>
                                <li>Make sure your device is carrier-unlocked and eSIM-compatible</li>
                                <li>Have a stable WiFi connection ready for eSIM installation</li>
                            </ul>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="${process.env.CLIENT_URL || 'https://esim.vizlync.net'}"
                               style="display: inline-block; background-color: #4a90e2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                                Visit Our Website
                            </a>
                        </div>

                        <hr style="margin: 20px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        console.log(`BillionConnect initial order email sent to: ${email}`);
        return response.data;
    } catch (error) {
        console.error('Failed to send BillionConnect initial order email:', error.response?.data || error.message);
        throw error;
    }
};

// Send order confirmation email to partner with QR code
const sendPartnerOrderEmail = async (email, orderDetails) => {
    try {
        let qrCodeAttachment = null;
        
        // Only process QR code if it exists
        if (orderDetails.qrCode) {
            try {
                // Convert data URL to base64 string
                const qrCodeBase64 = orderDetails.qrCode.split(',')[1];
                if (qrCodeBase64) {
                    qrCodeAttachment = {
                        content: qrCodeBase64,
                        mime_type: "image/png",
                        name: "esim-qr-code.png",
                        cid: "qrcode"
                    };
                }
            } catch (error) {
                console.error('Error processing QR code:', error);
                // Continue without QR code attachment
            }
        }
        
        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: `Your eSIM ${orderDetails.isTopUp ? 'TopUp ' : ''}Order - ${orderDetails.plan.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">Your eSIM ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Details</h2>
                        <p>Thank you for your ${orderDetails.isTopUp ? 'top-up ' : ''}order. Here are your eSIM details:</p>
                        
                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Plan Details</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666; width: 150px;"><strong>Order ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.order.id}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Plan Name</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.name}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Validity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.validityDays} days</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Data</strong></td>
                                    <td style="padding: 8px 0;">${
                                        orderDetails.plan.planType === 'Unlimited' ? 'Unlimited' :
                                        orderDetails.plan.planType === 'Custom' ? orderDetails.plan.customPlanData :
                                        `${orderDetails.plan.planData} ${orderDetails.plan.planDataUnit}`
                                    }</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Quantity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.quantity}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Order Total</strong></td>
                                    <td style="padding: 8px 0;">$${orderDetails.orderTotal}</td>
                                </tr>
                                ${orderDetails.startDate ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Start Date</strong></td>
                                    <td style="padding: 8px 0;">${new Date(orderDetails.startDate).toLocaleDateString()}</td>
                                </tr>
                                ` : ''}
                                ${orderDetails.validTime && orderDetails.plan?.provider?.name === 'Billionconnect' ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Activate Before</strong></td>
                                    <td style="padding: 8px 0;">${new Date(orderDetails.validTime).toLocaleString()}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>
                        
                        <div style="background-color: #f0f8f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">eSIM Details</h3>
                            ${orderDetails.isTopUp ? `
                                <!-- For top-up orders, show only ICCID and Parent Order ID -->
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>ICC ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.iccid || 'N/A'}</td>
                                    </tr>
                                    ${orderDetails.order.parentOrderId ? `
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Original Order ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.order.parentOrderId}</td>
                                    </tr>
                                    ` : ''}
                                </table>
                                <p style="color: #666; font-size: 14px; margin-top: 15px; font-style: italic;">
                                    This is a top-up order for your existing eSIM. No new installation required.
                                </p>
                            ` : `
                                <!-- For regular orders, show all eSIM details -->
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>ICC ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.iccid || 'N/A'}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>SMDP Address</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.smdpAddress || 'N/A'}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Local Profile Assistant (LPA)</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.lpaString || 'N/A'}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Access Point Name</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.accessPointName || 'N/A'}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Phone Number</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.phoneNumber || 'N/A'}</td>
                                    </tr>
                                </table>
                            `}
                        </div>

                        ${qrCodeAttachment && !orderDetails.isTopUp ? `
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin: 30px 0;">
                              <tr>
                                <td align="center">
                                  <h3 style="color: #111; margin-bottom: 10px;">Your eSIM QR Code</h3>
                                  <img src="${orderDetails.qrCode}"
                                       alt="eSIM QR Code"
                                       width="200"
                                       style="display: block; max-width: 200px; width: 100%; height: auto; margin: 15px auto;" />
                                  <p style="color: #666; margin-top: 10px;">Scan this QR code to install your eSIM</p>
                                  <p style="color: #666; margin-top: 10px;">if the QR code isn't displaying here, You can also download the QR code from the attachments section of this email</p>
                                </td>
                              </tr>
                            </table>
                          ` : ''}
                          

                        ${!orderDetails.isTopUp ? `
                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">Important Notes:</h4>
                            <ul style="color: #856404; margin: 5px 0; padding-left: 20px;">
                                ${qrCodeAttachment ? '<li>The QR code is attached to this email for easy downloading</li>' : ''}
                                <li>Make sure your screen brightness is high when scanning</li>
                            </ul>
                        </div>

                        <div style="background-color: #f0f8f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Installation Instructions</h3>

                            <div style="margin-bottom: 15px;">
                                <h4 style="color: #4a90e2;">For iPhone (iOS):</h4>
                                <ol style="padding-left: 20px;">
                                    <li>Open Settings</li>
                                    <li>Tap Mobile Services → Add eSIM</li>
                                    <li>Scan the QR code above</li>
                                    <li>Enter the PIN Code (if required)</li>
                                    <li>Tap Done</li>
                                </ol>
                            </div>

                            <div>
                                <h4 style="color: #4a90e2;">For Android & Other Devices:</h4>
                                <ol style="padding-left: 20px;">
                                    <li>Go to Settings → Cellular/Mobile Network</li>
                                    <li>Tap Add Cellular Plan / Add Mobile Network</li>
                                    <li>Scan the QR code above</li>
                                    <li>Follow the on-screen instructions</li>
                                </ol>
                            </div>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">Important Notes:</h4>
                            <ul style="color: #856404; padding-left: 20px;">
                                <li>Your device must be carrier-unlocked and eSIM-compatible</li>
                                <li>Connect to WiFi before scanning the QR code</li>
                                <li>The QR code is for one-time use only</li>
                            </ul>
                        </div>
                        ` : ''}

                        <hr style="margin: 20px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                    </div>
                `,
                attachments: qrCodeAttachment ? [qrCodeAttachment] : [],
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        return response.data;
    } catch (error) {
        console.error('Failed to send order confirmation email:', error.response?.data || error.message);
        throw new Error('Failed to send order confirmation email');
    }
};

// Send order notification email to admins
const sendAdminOrderEmail = async (adminEmails, orderDetails) => {
    try {
        if (!adminEmails || adminEmails.length === 0) {
            console.error('Cannot send order email: No admin emails provided');
            return;
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: adminEmails.map(email => ({
                    email_address: {
                        address: email
                    }
                })),
                subject: `New ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Alert - ${orderDetails.plan.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">New ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Alert</h2>
                        <p>A new eSIM ${orderDetails.isTopUp ? 'top-up ' : ''}order has been placed:</p>
                        
                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Order Details</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666; width: 150px;"><strong>Order ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.order.id}</td>
                                </tr>
                                ${orderDetails.isTopUp && orderDetails.order.parentOrderId ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Parent Order ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.order.parentOrderId}</td>
                                </tr>
                                ` : ''}
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Plan Name</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.name}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Product ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.productId || 'N/A'}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Provider</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.provider?.name || 'N/A'}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Network</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.networkName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Validity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.validityDays} days</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Data</strong></td>
                                    <td style="padding: 8px 0;">${
                                        orderDetails.plan.planType === 'Unlimited' ? 'Unlimited' :
                                        orderDetails.plan.planType === 'Custom' ? orderDetails.plan.customPlanData :
                                        `${orderDetails.plan.planData} ${orderDetails.plan.planDataUnit}`
                                    }</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Quantity</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.quantity}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Order Total</strong></td>
                                    <td style="padding: 8px 0;">$${orderDetails.orderTotal}</td>
                                </tr>
                                ${orderDetails.startDate ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Start Date</strong></td>
                                    <td style="padding: 8px 0;">${new Date(orderDetails.startDate).toLocaleDateString()}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>

                        <div style="background-color: #e8f4f8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Partner Details</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Name</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.firstName} ${orderDetails.partner.lastName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Email</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.email}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Partner ID</strong></td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.id}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #f0f8f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">eSIM Details</h3>
                            ${orderDetails.isTopUp ? `
                                <!-- For top-up orders, show only ICCID, Transaction ID, and Parent Order ID -->
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>ICC ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.iccid}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Transaction ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.walletAuthTransactionId}</td>
                                    </tr>
                                    ${orderDetails.order.parentOrderId ? `
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Original Order ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.order.parentOrderId}</td>
                                    </tr>
                                    ` : ''}
                                </table>
                                <p style="color: #666; font-size: 14px; margin-top: 15px; font-style: italic;">
                                    This is a top-up order for an existing eSIM.
                                </p>
                            ` : `
                                <!-- For regular orders, show all eSIM details -->
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>ICC ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.iccid}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>SMDP Address</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.smdpAddress}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Local Profile Assistant (LPA)</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.lpaString}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Access Point Name</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.accessPointName}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Phone Number</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.stock.phoneNumber || 'N/A'}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 8px 0; color: #666;"><strong>Transaction ID</strong></td>
                                        <td style="padding: 8px 0;">${orderDetails.walletAuthTransactionId}</td>
                                    </tr>
                                </table>
                            `}
                        </div>

                        <hr style="margin: 20px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        return response.data;
    } catch (error) {
        console.error('Failed to send admin order notification:', error.response?.data || error.message);
        // Don't throw error as this is a notification service
    }
};

// Send welcome email to newly created partner with credentials
const sendPartnerWelcomeEmail = async (partnerEmail, partnerData, password) => {
    try {
        const emailContent = {
            to: partnerEmail,
            subject: 'Welcome to eSIM Platform - Your Account Details',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #4a90e2;">Welcome to eSIM Platform!</h2>
                    <p>Dear ${partnerData.firstName} ${partnerData.lastName},</p>
                    <p>Your partner account has been successfully created. Below are your login credentials:</p>

                    <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="margin: 0 0 15px; color: #111;">Account Details</h3>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr style="border-bottom: 1px solid #e5e7eb;">
                                <td style="padding: 8px 0; color: #666; width: 150px;"><strong>Email</strong></td>
                                <td style="padding: 8px 0;">${partnerData.email}</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #e5e7eb;">
                                <td style="padding: 8px 0; color: #666;"><strong>Password</strong></td>
                                <td style="padding: 8px 0; font-family: monospace; background-color: #e5e7eb; padding: 4px 8px; border-radius: 4px;">${password}</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #e5e7eb;">
                                <td style="padding: 8px 0; color: #666;"><strong>Partner ID</strong></td>
                                <td style="padding: 8px 0;">${partnerData.id}</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #e5e7eb;">
                                <td style="padding: 8px 0; color: #666;"><strong>Business Name</strong></td>
                                <td style="padding: 8px 0;">${partnerData.businessName}</td>
                            </tr>
                        </table>
                    </div>

                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <h4 style="color: #856404; margin-top: 0;">Important Security Notice:</h4>
                        <ul style="color: #856404; margin: 5px 0; padding-left: 20px;">
                            <li>Please change your password after your first login</li>
                            <li>Keep your credentials secure and do not share them</li>
                            <li>Contact support if you have any issues accessing your account</li>
                        </ul>
                    </div>

                    <div style="background-color: #e8f4f8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="margin: 0 0 15px; color: #111;">Getting Started</h3>
                        <p>You can now:</p>
                        <ul style="padding-left: 20px;">
                            <li>Browse available eSIM plans</li>
                            <li>Place orders for your customers</li>
                            <li>Manage your wallet and transactions</li>
                            <li>Access API documentation for integration</li>
                        </ul>
                    </div>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="${process.env.CLIENT_URL || 'https://your-website.com'}"
                           style="display: inline-block; background-color: #4a90e2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                            Visit Our Website
                        </a>
                    </div>

                    <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                    <p>Welcome aboard!</p>

                    <hr style="margin: 20px 0;">
                    <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                </div>
            `
        };

        await sendEmail(emailContent);
        console.log(`Welcome email sent to partner: ${partnerEmail}`);
    } catch (error) {
        console.error('Failed to send partner welcome email:', error);
        throw error;
    }
};

// Send notification to admin about new partner creation
const sendAdminPartnerNotificationEmail = async (adminEmails, partnerData) => {
    try {
        if (!adminEmails || adminEmails.length === 0) {
            console.error('Cannot send admin notification: No admin emails provided');
            return;
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: adminEmails.map(email => ({
                    email_address: {
                        address: email
                    }
                })),
                subject: `New Partner Created - ${partnerData.businessName}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">New Partner Account Created</h2>
                        <p>A new partner account has been successfully created in the system.</p>

                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Partner Details</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666; width: 150px;"><strong>Partner ID</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.id}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Name</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.firstName} ${partnerData.lastName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Email</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.email}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Business Name</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.businessName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Business Email</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.businessEmail}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Phone Number</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.phoneNumber}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Markup Percentage</strong></td>
                                    <td style="padding: 8px 0;">${partnerData.markupPercentage}%</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; color: #666;"><strong>Created At</strong></td>
                                    <td style="padding: 8px 0;">${new Date(partnerData.createdAt).toLocaleString()}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #e8f4f8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Billing Address</h3>
                            <p style="margin: 5px 0;">${partnerData.billingAddressLine1}</p>
                            ${partnerData.billingAddressLine2 ? `<p style="margin: 5px 0;">${partnerData.billingAddressLine2}</p>` : ''}
                            <p style="margin: 5px 0;">${partnerData.billingCity}, ${partnerData.billingProvince}</p>
                            <p style="margin: 5px 0;">${partnerData.billingPostalCode}</p>
                        </div>

                        <div style="background-color: #f0f8f0; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <p style="color: #16a34a; margin: 0;"><strong>✓ Welcome email has been sent to the partner with their login credentials.</strong></p>
                        </div>

                        <hr style="margin: 20px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message from the eSIM Platform.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        if (response.data.error) {
            console.error('ZeptoMail API Error:', response.data.error);
            throw new Error(response.data.error.message || 'Failed to send email');
        }

        console.log(`Admin notification sent to ${adminEmails.length} admins for new partner: ${partnerData.email}`);
        return response.data;
    } catch (error) {       
        console.error('Failed to send admin partner notification:', error.response?.data || error.message);
        throw error;
    }
};


// Send API order confirmation email to partner
const sendApiPartnerOrderEmail = async (email, orderDetails) => {
    try {
        let qrCodeAttachment = null;

        // Only process QR code if it exists
        if (orderDetails.qrCode) {
            try {
                // Convert data URL to base64 string
                const qrCodeBase64 = orderDetails.qrCode.split(',')[1];
                if (qrCodeBase64) {
                    qrCodeAttachment = {
                        content: qrCodeBase64,
                        mime_type: "image/png",
                        name: "esim-qr-code.png",
                        cid: "qrcode"
                    };
                }
            } catch (error) {
                console.error('Error processing QR code:', error);
                // Continue without QR code attachment
            }
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: [{
                    email_address: {
                        address: email
                    }
                }],
                subject: `Your eSIM ${orderDetails.isTopUp ? 'TopUp ' : ''}Order (Via API) - ${orderDetails.plan.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">Your eSIM ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Details (Via API)</h2>
                        <p>Thank you for your ${orderDetails.isTopUp ? 'API top-up' : 'API'} order. Here are your eSIM details:</p>

                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Order Summary</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Order ID:</td>
                                    <td style="padding: 8px 0;">${orderDetails.order.id}</td>
                                </tr>
                                ${orderDetails.isTopUp && orderDetails.order.parentOrderId ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Parent Order ID:</td>
                                    <td style="padding: 8px 0;">${orderDetails.order.parentOrderId}</td>
                                </tr>
                                ` : ''}
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Plan:</td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.name}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Total Amount:</td>
                                    <td style="padding: 8px 0;">$${orderDetails.orderTotal}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Order Method:</td>
                                    <td style="padding: 8px 0;"><strong>API</strong></td>
                                </tr>
                                ${orderDetails.startDate ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Start Date:</td>
                                    <td style="padding: 8px 0;">${orderDetails.startDate}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>

                        ${orderDetails.iccid ? `
                        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">eSIM Details</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">ICCID:</td>
                                    <td style="padding: 8px 0; font-family: monospace;">${orderDetails.iccid}</td>
                                </tr>
                                ${orderDetails.smdpAddress ? `
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">SMDP Address:</td>
                                    <td style="padding: 8px 0; font-family: monospace;">${orderDetails.smdpAddress}</td>
                                </tr>
                                ` : ''}
                                ${orderDetails.activationCode ? `
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">Activation Code:</td>
                                    <td style="padding: 8px 0; font-family: monospace; word-break: break-all;">${orderDetails.activationCode}</td>
                                </tr>
                                ` : ''}
                                ${orderDetails.accessPointName ? `
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">APN:</td>
                                    <td style="padding: 8px 0; font-family: monospace;">${orderDetails.accessPointName}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>
                        ` : ''}

                        ${qrCodeAttachment ? `
                        <div style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
                            <h3 style="margin: 0 0 15px; color: #111;">QR Code</h3>
                            <p>Scan this QR code with your device to install the eSIM:</p>
                            <img src="cid:qrcode" alt="eSIM QR Code" style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 8px;">
                        </div>
                        ` : ''}

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                            <h4 style="margin: 0 0 10px; color: #856404;">Installation Instructions</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #856404;">
                                <li>Go to Settings > Cellular/Mobile Data</li>
                                <li>Tap "Add Cellular Plan" or "Add eSIM"</li>
                                <li>Scan the QR code above or enter the activation code manually</li>
                                <li>Follow the on-screen instructions to complete setup</li>
                            </ol>
                        </div>

                        <p style="color: #666; font-size: 14px; margin-top: 30px;">
                            This order was placed via API. If you have any questions, please contact our support team.
                        </p>

                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply.</p>
                    </div>
                `,
                attachments: qrCodeAttachment ? [qrCodeAttachment] : undefined,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        console.log(`API order email sent to partner: ${email}`);
        return response.data;
    } catch (error) {
        console.error('Failed to send API partner order email:', error.response?.data || error.message);
        throw error;
    }
};

// Send API order notification email to admins
const sendApiAdminOrderEmail = async (adminEmails, orderDetails) => {
    try {
        if (!adminEmails || adminEmails.length === 0) {
            console.log('No admin emails provided for API order notification');
            return;
        }

        const response = await axios.post(
            process.env.ZEPTO_API_URL,
            {
                from: {
                    address: process.env.ZEPTO_FROM_EMAIL,
                    name: process.env.ZEPTO_FROM_EMAIL_NAME
                },
                to: adminEmails.map(email => ({
                    email_address: {
                        address: email
                    }
                })),
                subject: `New ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Alert (Via API) - ${orderDetails.plan.name}`,
                htmlbody: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #4a90e2;">New ${orderDetails.isTopUp ? 'TopUp ' : ''}Order Alert (Via API)</h2>
                        <p>A new eSIM ${orderDetails.isTopUp ? 'top-up ' : ''}order has been placed via API:</p>

                        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Order Details</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Order ID:</td>
                                    <td style="padding: 8px 0;">${orderDetails.order.id}</td>
                                </tr>
                                ${orderDetails.isTopUp && orderDetails.order.parentOrderId ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Parent Order ID:</td>
                                    <td style="padding: 8px 0;">${orderDetails.order.parentOrderId}</td>
                                </tr>
                                ` : ''}
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Plan:</td>
                                    <td style="padding: 8px 0;">${orderDetails.plan.name}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Total Amount:</td>
                                    <td style="padding: 8px 0;">$${orderDetails.orderTotal}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Order Method:</td>
                                    <td style="padding: 8px 0;"><strong>API</strong></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Quantity:</td>
                                    <td style="padding: 8px 0;">${orderDetails.quantity}</td>
                                </tr>
                                ${orderDetails.startDate ? `
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Start Date:</td>
                                    <td style="padding: 8px 0;">${orderDetails.startDate}</td>
                                </tr>
                                ` : ''}
                                <tr style="border-bottom: 1px solid #e5e7eb;">
                                    <td style="padding: 8px 0; font-weight: bold;">Order Date:</td>
                                    <td style="padding: 8px 0;">${new Date().toLocaleString()}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">Partner Information</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">Partner ID:</td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.id}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">Name:</td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.firstName} ${orderDetails.partner.lastName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #b3d9f2;">
                                    <td style="padding: 8px 0; font-weight: bold;">Email:</td>
                                    <td style="padding: 8px 0;">${orderDetails.partner.email}</td>
                                </tr>
                            </table>
                        </div>

                        ${orderDetails.iccid ? `
                        <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 15px; color: #111;">eSIM Information</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #bae6fd;">
                                    <td style="padding: 8px 0; font-weight: bold;">ICCID:</td>
                                    <td style="padding: 8px 0; font-family: monospace;">${orderDetails.iccid}</td>
                                </tr>
                                ${orderDetails.smdpAddress ? `
                                <tr style="border-bottom: 1px solid #bae6fd;">
                                    <td style="padding: 8px 0; font-weight: bold;">SMDP Address:</td>
                                    <td style="padding: 8px 0; font-family: monospace;">${orderDetails.smdpAddress}</td>
                                </tr>
                                ` : ''}
                                ${orderDetails.activationCode ? `
                                <tr style="border-bottom: 1px solid #bae6fd;">
                                    <td style="padding: 8px 0; font-weight: bold;">Activation Code:</td>
                                    <td style="padding: 8px 0; font-family: monospace; word-break: break-all;">${orderDetails.activationCode}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>
                        ` : ''}

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404;">
                                <strong>Note:</strong> This order was placed via API integration. Please monitor the order status and ensure proper fulfillment.
                            </p>
                        </div>

                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #666; font-size: 12px;">This is an automated notification from the eSIM Platform.</p>
                    </div>
                `,
                track_clicks: false,
                track_opens: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': process.env.ZEPTO_API_KEY
                }
            }
        );

        console.log(`API order admin notification sent to ${adminEmails.length} admins`);
        return response.data;
    } catch (error) {
        console.error('Failed to send API admin order notification:', error.response?.data || error.message);
        // Don't throw error as this is a notification service
    }
};

module.exports = {
    sendOTPEmail,
    sendPasswordResetEmail,
    sendLowBalanceEmail,
    sendWalletCreditEmail,
    sendLowStockEmail,
    sendBillionConnectInitialOrderEmail,
    sendPartnerOrderEmail,
    sendAdminOrderEmail,
    sendPartnerWelcomeEmail,
    sendAdminPartnerNotificationEmail,
    sendApiPartnerOrderEmail,
    sendApiAdminOrderEmail
};
