# Setting Up GitHub Secrets for Deployment

This guide will help you set up the necessary GitHub Secrets for deploying your eSIM Platform backend to AWS EC2.

## Required GitHub Secrets

### AWS Credentials

1. `AWS_ACCESS_KEY_ID`: Your AWS access key
2. `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
3. `AWS_REGION`: Your AWS region (e.g., us-east-1)

### EC2 Connection Details

4. `EC2_HOST`: Your EC2 instance's public IP address
5. `EC2_USERNAME`: The username for SSH access (usually 'ubuntu')
6. `EC2_SSH_KEY`: The private SSH key for accessing your EC2 instance

### Domain Configuration

7. `DOMAIN_NAME`: Your domain name (e.g., api.yourdomain.com)
8. `ADMIN_EMAIL`: Your email address for SSL certificate notifications

### Application Environment Variables

9. `PORT`: The port your app runs on (usually 3000)
10. `NODE_ENV`: Set to 'production'
11. `CLIENT_URL`: Your frontend URL
12. `DB_HOST`: Your RDS endpoint (e.g., 'your-database.abcdefghijkl.us-east-1.rds.amazonaws.com')
13. `DB_USER`: Database username (usually 'root')
14. `DB_PASSWORD`: Database password
15. `DB_NAME`: Database name
16. `JWT_SECRET`: Secret for JWT tokens
17. `JWT_REFRESH_SECRET`: Secret for JWT refresh tokens
18. `JWT_EXPIRES_IN`: JWT expiration time
19. `JWT_REFRESH_EXPIRES_IN`: JWT refresh token expiration time
20. `ZEPTO_API_URL`: ZeptoMail API URL
21. `ZEPTO_API_KEY`: ZeptoMail API key
22. `ZEPTO_FROM_EMAIL`: ZeptoMail sender email
23. `MAX_LOGIN_ATTEMPTS`: Maximum login attempts
24. `LOGIN_TIMEOUT`: Login timeout duration
25. `MOBIMATTER_API_URL`: Mobimatter API URL
26. `MOBIMATTER_API_KEY`: Mobimatter API key
27. `MOBIMATTER_MERCHANT_ID`: Mobimatter merchant ID
28. `BILLIONCONNECT_API_URL`: BillionConnect API URL
29. `BILLIONCONNECT_CHANNEL_ID`: BillionConnect channel ID
30. `BILLIONCONNECT_APP_SECRET`: BillionConnect app secret
31. `API_BASE_URL`: Base URL for your API
32. `DEV_API_URL`: Development API URL
33. `PROD_API_URL`: Production API URL
34. `ZEPTO_FROM_EMAIL_NAME`: ZeptoMail sender name

## How to Add GitHub Secrets

1. Go to your GitHub repository
2. Click on "Settings" tab
3. In the left sidebar, click on "Secrets and variables" > "Actions"
4. Click on "New repository secret"
5. Enter the name of the secret (e.g., `AWS_ACCESS_KEY_ID`)
6. Enter the value of the secret
7. Click "Add secret"
8. Repeat for all required secrets

## Generating SSH Key for EC2

To generate an SSH key for EC2 access:

1. Run the following command on your local machine:
   ```
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/ec2_key
   ```
2. Add the public key to your EC2 instance:
   ```
   ssh-copy-id -i ~/.ssh/ec2_key.pub ubuntu@your-ec2-public-ip
   ```
3. Add the private key as a GitHub Secret:
   ```
   cat ~/.ssh/ec2_key
   ```
   Copy the entire output (including the BEGIN and END lines) and add it as the `EC2_SSH_KEY` secret.

## Verifying Secrets

After adding all secrets, you can verify them by:

1. Going to your GitHub repository
2. Clicking on "Settings" tab
3. In the left sidebar, clicking on "Secrets and variables" > "Actions"
4. Checking that all required secrets are listed

## Next Steps

After setting up all GitHub Secrets:

1. Push your code to the main branch of your GitHub repository
2. GitHub Actions will automatically deploy your application to the EC2 instance
3. Monitor the GitHub Actions workflow to ensure successful deployment

## SSL Certificate Renewal

The SSL certificates are set to auto-renew twice daily. If you encounter any issues, you can manually renew the certificates by SSHing into your EC2 instance and running the `setup-ssl.sh` script with your domain and email as arguments.

cd ~/esim-project ./setup-ssl.sh api.vizlync.net <EMAIL>

## Initial EC2 Setup with docker-compose and docker
#!/bin/bash

# Update package index
sudo apt update -y

# Install prerequisites
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common

# Install Docker
sudo apt install -y docker.io

# Enable and start Docker service
sudo systemctl enable docker
sudo systemctl start docker

# Install Docker Compose
DOCKER_COMPOSE_VERSION="v2.20.2"
sudo curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Apply executable permissions
sudo chmod +x /usr/local/bin/docker-compose

# Add current user (ubuntu) to docker group to run docker without sudo
sudo usermod -aG docker ubuntu

# Verify installations
docker --version
docker-compose --version

echo "Docker and Docker Compose installation complete."


## Initial  Nginx setup (Dockerfile.nginx)
FROM nginx:1.21-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Create HTTP-only configuration for initial setup
RUN echo 'server { \
    listen 80; \
    server_name localhost api.vizlync.net; \
    \
    # For Let'"'"'s Encrypt HTTP-01 challenge \
    location ^~ /.well-known/acme-challenge/ { \
        root /var/www/certbot; \
        try_files $uri =404; \
    } \
    \
    # Serve the application over HTTP initially \
    location / { \
        proxy_pass http://esim-backend:3000; \
        proxy_http_version 1.1; \
        proxy_set_header Upgrade $http_upgrade; \
        proxy_set_header Connection '"'"'upgrade'"'"'; \
        proxy_set_header Host $host; \
        proxy_set_header X-Real-IP $remote_addr; \
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto $scheme; \
        proxy_cache_bypass $http_upgrade; \
    } \
}' > /etc/nginx/conf.d/app.conf

# Create ACME challenge directory
RUN mkdir -p /var/www/certbot/.well-known/acme-challenge/

# Create a test file for ACME challenge verification
RUN echo "This is a test file for ACME challenge verification" > /var/www/certbot/.well-known/acme-challenge/test-file

# Set proper permissions
RUN chmod -R 755 /var/www/certbot

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]





## Docker Nginx manual setup for test
docker-compose exec nginx sh -c "cat > /etc/nginx/conf.d/app.conf << 'EOF'
server {
    listen 80;
    server_name localhost api.vizlync.net;

    # For Let's Encrypt HTTP-01 challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
        
        # Add debug headers
        add_header X-Debug-Info \"Serving from ACME challenge handler\" always;
        add_header Content-Type text/plain;
    }

    # Forward all other requests to the app
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF"

## Docker commands

docker-compose logs nginx
docker-compose exec nginx nginx -s reload

docker-compose down
docker-compose build nginx
docker-compose up -d


http://api.vizlync.net/.well-known/acme-challenge/test-file

docker-compose exec nginx cat /etc/nginx/conf.d/app.conf

docker-compose logs app

# Check if there's a running certbot process for certificate generation
docker ps | grep certbot

# Check the logs of the certbot container
docker logs certbot

# If there's a separate certbot process running for certificate generation, find it
docker ps -a | grep certbot


# Check if there are any exited certbot containers
docker ps -a --filter "ancestor=certbot/certbot"

# Check for any exited containers (including temporary ones)
docker ps -a | head -20

# Check if there are any running processes related to certbot
docker-compose ps

# Look for any recent container activity
docker events --since 10m | grep certbot

# Check Nginx configuration
docker exec nginx nginx -t


# Check if setup-ssl.sh is still running
ps aux | grep setup-ssl

# Check if there are any certbot processes running
ps aux | grep certbot

# Dry-run won't actually request a certificate but will test the process
docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot \
  --email <EMAIL> --agree-tos --no-eff-email \
  -d api.vizlync.net \
  --dry-run --verbose


  # Stop the dry-run (Ctrl+C)
# Then test basic connectivity to Let's Encrypt
curl -v https://acme-v02.api.letsencrypt.org/directory


# Stop the hanging certbot process (Ctrl+C)

# Test if Docker containers can reach the internet
docker run --rm alpine:latest ping -c 3 *******

# Test if Docker can reach Let's Encrypt directly
docker run --rm alpine:latest wget -O- https://acme-staging-v02.api.letsencrypt.org/directory







# Check if the volume mount is working correctly
docker-compose exec nginx ls -la /var/www/certbot/.well-known/acme-challenge/

# Test creating a file from inside the container
docker-compose exec nginx sh -c "echo 'test' > /var/www/certbot/.well-known/acme-challenge/docker-test"

# Check if it appears on the host
ls -la nginx/certbot/www/.well-known/acme-challenge/ 2>/dev/null || echo "Directory not found"



# Install certbot on the host system
sudo apt update && sudo apt install certbot -y

# Try certbot directly on the host with staging
sudo certbot certonly --webroot --webroot-path=/home/<USER>/esim-project/nginx/certbot/www \
  --email <EMAIL> --agree-tos --no-eff-email \
  -d api.vizlync.net \
  --staging --verbose



  # Delete certificates from host
sudo rm -rf /etc/letsencrypt/live/api.vizlync.net
sudo rm -rf /etc/letsencrypt/archive/api.vizlync.net
sudo rm -f /etc/letsencrypt/renewal/api.vizlync.net.conf

# Delete certificates from Docker location
sudo rm -rf nginx/certbot/conf/live/api.vizlync.net
sudo rm -rf nginx/certbot/conf/archive/api.vizlync.net
sudo rm -f nginx/certbot/conf/renewal/api.vizlync.net.conf

# Verify deletion
echo "Verifying certificates are deleted:"
sudo ls -la /etc/letsencrypt/live/ 2>/dev/null || echo "Live directory empty"
sudo ls -la /etc/letsencrypt/archive/ 2>/dev/null || echo "Archive directory empty"
ls -la nginx/certbot/conf/live/ 2>/dev/null || echo "Docker live directory empty"





# Check if certificates were created in the host location
sudo find /etc/letsencrypt -name "*api.vizlync.net*" -type d
sudo find /etc/letsencrypt -name "*api.vizlync.net*" -type f


# Test just the copying logic with current certificates
DOMAIN="api.vizlync.net"

# Run the new copying section manually
echo "Copying certificates to Docker volume location..."
sudo mkdir -p nginx/certbot/conf/live nginx/certbot/conf/archive nginx/certbot/conf/renewal

sleep 2

echo "Checking certificate locations..."
sudo ls -la /etc/letsencrypt/live/ 2>/dev/null || echo "Live directory not accessible"

if sudo [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
    echo "Found live certificates for $DOMAIN, copying..."
    sudo cp -r /etc/letsencrypt/live/$DOMAIN nginx/certbot/conf/live/
    echo "✅ Live certificates copied"
else
    echo "❌ Live certificates not found for $DOMAIN"
fi

# Verify
echo "Verifying copied certificates:"
ls -la nginx/certbot/conf/live/



# Check if certificates are already copied
ls -la nginx/certbot/conf/live/api.vizlync.net/ 2>/dev/null && echo "✅ Certificates already copied" || echo "❌ Need to copy certificates"


# Test HTTPS access
curl -I https://api.vizlync.net

# Test HTTP redirect
curl -I http://api.vizlync.net

# Check Nginx status
docker-compose logs nginx

# Check if SSL configuration is actually loaded
docker exec nginx nginx -T | grep -A 10 -B 5 "443\|ssl"

# Check if certificates are accessible inside the container
docker exec nginx ls -la /etc/letsencrypt/live/api.vizlync.net/


# Test Nginx configuration
docker exec nginx nginx -t

# Check what configuration files are loaded
docker exec nginx ls -la /etc/nginx/conf.d/

# Check the actual Nginx configuration being used
docker exec nginx cat /etc/nginx/conf.d/app.conf



# Test the login with real credentials and watch the logs
docker-compose logs -f app &

## database optimization
node scripts/optimize-database.js