-- Performance optimization for partner plans endpoint
-- This migration adds specific indexes to optimize the /api/esim-plans/partner endpoint

-- Drop existing indexes that might conflict (MySQL compatible syntax)
-- Note: These will fail silently if indexes don't exist, which is fine
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_schema = DATABASE() AND table_name = 'esimplans' AND index_name = 'idx_esimplans_partner_optimized') > 0,
    'DROP INDEX idx_esimplans_partner_optimized ON esimplans',
    'SELECT 1'));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Critical composite index for the main partner query pattern
-- This covers: status, isActive, category, createdAt (for ORDER BY)
-- This is the most important index for performance
CREATE INDEX idx_esimplans_partner_main_optimized ON esimplans(
    status, 
    isActive, 
    category, 
    createdAt DESC
);

-- Index for search functionality (name and networkName)
CREATE INDEX idx_esimplans_search_optimized ON esimplans(
    status, 
    isActive, 
    category, 
    name(100), 
    networkName(100)
);

-- Index for region filtering
CREATE INDEX idx_esimplans_region_optimized ON esimplans(
    status, 
    isActive, 
    category, 
    region(100)
);

-- Optimized index for country filtering through junction table
-- This helps with the $countries.id$ filter
CREATE INDEX idx_esimplancountries_country_optimized ON esimplancountries(
    countryId, 
    esimPlanId
);

-- Reverse index for better join performance
CREATE INDEX idx_esimplancountries_plan_optimized ON esimplancountries(
    esimPlanId, 
    countryId
);

-- Provider status index for filtering active providers
CREATE INDEX idx_providers_status_active ON providers(
    status, 
    id, 
    name(50)
);

-- Countries index for the countries query
CREATE INDEX idx_countries_active_optimized ON countries(
    isActive, 
    name(100), 
    id
);

-- Composite index for pagination with all common filters
CREATE INDEX idx_esimplans_pagination_optimized ON esimplans(
    status, 
    isActive, 
    category, 
    providerId,
    createdAt DESC, 
    id
);

-- Index for regions query optimization
CREATE INDEX idx_esimplans_regions_query ON esimplans(
    status, 
    isActive, 
    region(100)
) WHERE region IS NOT NULL;

-- Users index for partner lookup optimization
CREATE INDEX idx_users_partner_optimized ON users(
    id, 
    markupPercentage, 
    isActive
);

-- Analyze tables to update statistics after creating indexes
ANALYZE TABLE esimplans;
ANALYZE TABLE esimplancountries;
ANALYZE TABLE countries;
ANALYZE TABLE providers;
ANALYZE TABLE users;

-- Show index usage information (for debugging)
-- SHOW INDEX FROM esimplans WHERE Key_name LIKE '%partner%' OR Key_name LIKE '%optimized%';
