const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { isAuthenticated, isAdmin, isPartner } = require('../middleware/auth');
const {
    getEsimPlans,
    getEsimPlan,
    createEsimPlan,
    createBulkEsimPlans,
    updateEsimPlan,
    deleteEsimPlan,
    updateEsimPlanStatus,
    updateProviderPlansVisibility,
    updateEsimPlanPrice,
    resetEsimPlanPrice,
    getPartnerEsimPlans,
    getPartnerEsimPlan,
    toggleStartDate,
    updateStockThreshold,
    syncExternalPlans,
    syncBillionConnectPlans,
    exportPlans,
    syncMobimatterPlans,
    getExchangeRateInfo,
    refreshExchangeRate
} = require('../controllers/esimPlanController');
const esimStockController = require('../controllers/esimStockController');
const { EsimPlan } = require('../models');
const { Op } = require('sequelize');
const { syncLimiter, partnerPlanLimiter, clientApiLimiter } = require('../middleware/rateLimiter');

// Admin Routes
// Get unique regions - this specific route must come before /:id
router.get('/regions', isAuthenticated, async (req, res) => {
    try {
        const regions = await EsimPlan.findAll({
            attributes: ['region'],
            group: ['region'],
            where: {
                region: {
                    [Op.not]: null
                }
            }
        });

        res.json(regions.map(r => r.region).filter(Boolean));
    } catch (error) {
        console.error('Error fetching regions:', error);
        res.status(500).json({ message: 'Failed to fetch regions' });
    }
});

// Partner specific routes with higher rate limits to avoid 429 errors
router.get('/partner', isAuthenticated, partnerPlanLimiter, getPartnerEsimPlans);
router.get('/partner/:id', isAuthenticated, partnerPlanLimiter, getPartnerEsimPlan);

// Get all eSIM plans
router.get('/', isAuthenticated, isAdmin, getEsimPlans);

// Create new eSIM plan
router.post('/', isAuthenticated, isAdmin, createEsimPlan);

// Create multiple eSIM plans from bulk upload
router.post('/bulk', isAuthenticated, isAdmin, createBulkEsimPlans);

// Export plans - this route should come before /:id to avoid conflict
router.get('/export', isAuthenticated, isAdmin, exportPlans);

// Download bulk upload instructions PDF
router.get('/download/bulk-upload-instructions', isAuthenticated, isAdmin, (req, res) => {
    try {
        const pdfPath = path.join(__dirname, '../../public/Bulk Upload Instructions.pdf');

        // Check if file exists
        if (!fs.existsSync(pdfPath)) {
            return res.status(404).json({ message: 'PDF file not found' });
        }

        // Set headers for PDF download
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename="Bulk Upload Instructions.pdf"');

        // Send the file
        res.sendFile(pdfPath);
    } catch (error) {
        console.error('Error serving PDF file:', error);
        res.status(500).json({ message: 'Failed to download PDF file' });
    }
});

// Sync external provider plans with a specialized rate limiter
router.post('/sync', isAuthenticated, isAdmin, syncLimiter, syncExternalPlans);

// Sync BillionConnect plans specifically
router.post('/sync/billionconnect', isAuthenticated, isAdmin, syncLimiter, syncBillionConnectPlans);

// Sync Mobimatter plans specifically
router.post('/sync/mobimatter', isAuthenticated, isAdmin, syncLimiter, syncMobimatterPlans);

// Update eSIM plan status (visible/hidden)
router.patch('/:id/status', isAuthenticated, isAdmin, updateEsimPlanStatus);

// Update all plans visibility for a specific provider
router.put('/provider/:providerId/visibility', isAuthenticated, isAdmin, updateProviderPlansVisibility);

// Update eSIM plan selling price
router.patch('/:id/price', isAuthenticated, isAdmin, updateEsimPlanPrice);

// Reset eSIM plan selling price
router.patch('/:id/reset-price', isAuthenticated, isAdmin, resetEsimPlanPrice);

// Toggle eSIM plan start date
router.patch('/:id/start-date-toggle', isAuthenticated, isAdmin, toggleStartDate);

// Update eSIM plan stock threshold
router.put('/:id/stock-threshold', isAuthenticated, isAdmin, updateStockThreshold);

// Update eSIM plan
router.put('/:id', isAuthenticated, isAdmin, updateEsimPlan);

// Delete eSIM plan
router.delete('/:id', isAuthenticated, isAdmin, deleteEsimPlan);

// Get single eSIM plan - this generic route should come last
router.get('/:id', isAuthenticated, isAdmin, getEsimPlan);

// Stock Management Routes
router.get('/:planId/stock', isAuthenticated, isAdmin, esimStockController.getStockByPlanId);
router.post('/:planId/stock', isAuthenticated, isAdmin, esimStockController.addStock);
router.post('/:planId/stock/bulk', isAuthenticated, isAdmin, esimStockController.addBulkStock);
router.get('/:planId/stock/:stockId', isAuthenticated, isAdmin, esimStockController.getStockDetails);
router.put('/:planId/stock/:stockId', isAuthenticated, isAdmin, esimStockController.updateStock);
router.delete('/:planId/stock/:stockId', isAuthenticated, isAdmin, esimStockController.deleteStock);

// Exchange rate management routes
router.get('/exchange-rate/info', isAuthenticated, isAdmin, getExchangeRateInfo);
router.post('/exchange-rate/refresh', isAuthenticated, isAdmin, refreshExchangeRate);

module.exports = router;
