import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, DollarSign, Clock, Wifi, WifiOff } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/axios';

/**
 * Test component for Exchange Rate functionality
 * This component can be used to test the real-time exchange rate features
 */
export default function ExchangeRateTest() {
    const [exchangeRateInfo, setExchangeRateInfo] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);
    const { toast } = useToast();

    const fetchExchangeRateInfo = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/esim-plans/exchange-rate/info');
            if (response.data.success) {
                setExchangeRateInfo(response.data.data);
            } else {
                throw new Error(response.data.message || 'Failed to fetch exchange rate info');
            }
        } catch (error) {
            console.error('Error fetching exchange rate info:', error);
            toast({
                title: 'Error',
                description: 'Failed to fetch exchange rate information',
                variant: 'destructive',
            });
        } finally {
            setLoading(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            const response = await api.post('/api/esim-plans/exchange-rate/refresh');
            
            if (response.data.success) {
                // Fetch updated info
                await fetchExchangeRateInfo();
                
                toast({
                    title: 'Success',
                    description: `Exchange rate refreshed! New rate: 1 CNY = ${response.data.data.newRate} USD`,
                });
            } else {
                throw new Error(response.data.message || 'Failed to refresh exchange rate');
            }
        } catch (error) {
            console.error('Error refreshing exchange rate:', error);
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to refresh exchange rate',
                variant: 'destructive',
            });
        } finally {
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchExchangeRateInfo();
    }, []);

    if (loading) {
        return (
            <Card className="w-full max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5" />
                        Exchange Rate Test
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center py-8">
                        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
                        Loading exchange rate information...
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5" />
                        Exchange Rate Test
                    </CardTitle>
                    <Button 
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="bg-green-600 hover:bg-green-700"
                    >
                        <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        {refreshing ? 'Refreshing...' : 'Refresh Rate'}
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="space-y-6">
                {exchangeRateInfo ? (
                    <>
                        {/* Current Rate */}
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <h3 className="font-semibold text-lg mb-2">Current Exchange Rate</h3>
                            <div className="text-2xl font-bold text-blue-600">
                                1 CNY = {exchangeRateInfo.currentRate} USD
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                                {exchangeRateInfo.isCached ? (
                                    <Badge variant="secondary" className="flex items-center gap-1">
                                        <WifiOff className="w-3 h-3" />
                                        Cached
                                    </Badge>
                                ) : (
                                    <Badge variant="default" className="flex items-center gap-1">
                                        <Wifi className="w-3 h-3" />
                                        Live
                                    </Badge>
                                )}
                                {exchangeRateInfo.cacheExpiry && (
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        Expires: {new Date(exchangeRateInfo.cacheExpiry).toLocaleTimeString()}
                                    </Badge>
                                )}
                            </div>
                        </div>

                        {/* API Status */}
                        <div>
                            <h3 className="font-semibold mb-3">API Providers Status</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {exchangeRateInfo.availableAPIs.map((api, index) => (
                                    <div key={index} className="border rounded-lg p-3">
                                        <div className="flex justify-between items-center">
                                            <span className="font-medium">{api.name}</span>
                                            <Badge 
                                                variant={api.keyConfigured ? "success" : "secondary"}
                                            >
                                                {api.keyConfigured ? "Available" : "Key Required"}
                                            </Badge>
                                        </div>
                                        {api.requiresKey && !api.keyConfigured && (
                                            <p className="text-sm text-gray-500 mt-1">
                                                API key not configured
                                            </p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Fallback Rate */}
                        <div className="bg-yellow-50 p-4 rounded-lg">
                            <h3 className="font-semibold mb-2">Fallback Rate</h3>
                            <p className="text-sm text-gray-600">
                                If all APIs fail, the system will use: <strong>1 CNY = {exchangeRateInfo.fallbackRate} USD</strong>
                            </p>
                        </div>

                        {/* Test Conversions */}
                        <div>
                            <h3 className="font-semibold mb-3">Sample Conversions</h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                {[10, 50, 100, 500].map(cny => (
                                    <div key={cny} className="text-center p-3 border rounded">
                                        <div className="font-semibold">{cny} CNY</div>
                                        <div className="text-sm text-gray-500">↓</div>
                                        <div className="text-blue-600 font-medium">
                                            ${(cny * exchangeRateInfo.currentRate).toFixed(2)} USD
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        No exchange rate information available
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
