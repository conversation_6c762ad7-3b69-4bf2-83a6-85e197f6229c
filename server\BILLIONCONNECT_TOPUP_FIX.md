# BillionConnect Topup Order Fix

## Problem Description

BillionConnect topup orders were failing with error code `1062` and message `渠道订单号已存在` (Channel order ID already exists). This was happening because the system was incorrectly trying to create both F040 and F007 orders with the same channel order ID.

### Error Flow (Before Fix)
1. **F040 API Call**: `order_user_timestamp` → Creates initial order
2. **F007 API Call**: `order_user_timestamp` → **FAILS** with "Channel order ID already exists"

### Root Cause
The system was treating topup orders the same as regular orders, calling both F040 (initial order creation) and F007 (recharge) APIs, when topup orders should only use F007.

## Solution

### Key Changes Made

#### 1. Skip F040 API for Topup Orders
- **File**: `server/src/controllers/orderController.js`
- **Change**: Modified order creation flow to skip F040 API call for BillionConnect topup orders
- **Logic**: Check `cartItem.isTopUp && cartItem.parentOrderId` before creating external order

#### 2. Use Unique Channel Order ID for F007
- **File**: `server/src/controllers/orderController.js` 
- **Change**: Generate unique channel order ID by appending `_topup` suffix
- **Before**: `order_8ef58573-525d-11f0-966a-0ae1562662bd_1752648199272`
- **After**: `order_8ef58573-525d-11f0-966a-0ae1562662bd_1752648199272_topup`

#### 3. Skip N009 Webhook Processing for Topup Orders
- **File**: `server/src/controllers/orderController.js`
- **Change**: Added check for `providerMetadata.rechargeOrder === true` to skip N009 processing
- **Reason**: Topup orders don't generate new ICCID/QR codes, so N009 processing is unnecessary

### Fixed Flow (After Fix)

#### Regular BillionConnect Orders
1. **F040 API Call**: Creates initial order with new ICCID
2. **N009 Webhook**: Processes ICCID and QR code information

#### BillionConnect Topup Orders  
1. **F007 API Call Only**: Adds data to existing ICCID (no F040, no N009)
2. **Unique Channel Order ID**: Uses `_topup` suffix to avoid conflicts

## Code Changes

### Order Creation Logic
```javascript
// Different flow for BillionConnect vs Mobimatter
let externalOrder = null;
let externalOrderId = null;

if (provider.name === 'Billionconnect') {
    // Check if this is a top-up order
    if (cartItem.isTopUp && cartItem.parentOrderId) {
        // For BillionConnect top-up orders, skip F040 and use F007 API directly
        // Use F007 API for recharge order with unique channel order ID
        const rechargeChannelOrderId = `${orderPayload.customerReference}_topup`;
        
        const rechargeOrder = await billionconnectService.createRechargeOrder({
            channelOrderId: rechargeChannelOrderId,
            iccidArray: [parentOrder.stock.iccid],
            skuId: plan.externalSkuId,
            copies: cartItem.quantity,
            totalAmount: cartItem.calculatedPrice.toString(),
            comment: `Top-up for order ${cartItem.parentOrderId}`
        });
    } else {
        // For regular BillionConnect orders, create F040 order and wait for N009 webhook
        externalOrder = await providerService.createOrder(orderPayload);
    }
}
```

### N009 Webhook Processing
```javascript
// Check if this is a topup/recharge order
if (order.providerMetadata && order.providerMetadata.rechargeOrder === true) {
    console.log('Skipping N009 webhook processing for topup order:', {
        orderId: order.id,
        externalOrderId: orderId,
        reason: 'Topup orders do not generate new ICCID and QR code'
    });
    
    return res.json({
        tradeCode: '1000',
        tradeMsg: 'Topup order webhook acknowledged but not processed'
    });
}
```

## Testing

### Test Results
- ✅ Channel order ID generation with unique suffix
- ✅ Order flow logic (F040 vs F007 only)
- ✅ Error scenario fix verification
- ✅ Webhook processing logic

### Manual Testing
Run the test script to verify the fix:
```bash
node test_topup_fix.js
```

## Benefits

1. **Eliminates "Channel order ID already exists" error**
2. **Correct API usage**: Only F007 for topups, F040+N009 for regular orders
3. **Improved performance**: Fewer API calls for topup orders
4. **Better error handling**: Clear separation between order types
5. **Maintains backward compatibility**: Regular orders work unchanged

## Additional Fix: Order Status Display

### Problem
Topup orders were showing "pending" status in the order details page instead of "completed", even after successful F007 recharge order creation.

### Root Cause
The order status was never updated to "completed" after successful topup order creation. Only the `providerOrderStatus` was set to "PENDING", but the main `status` field remained "pending".

### Solution
Updated the topup order creation logic to immediately set:
- `status: 'completed'` - Shows green "completed" badge in UI
- `providerOrderStatus: 'ACTIVE'` - Reflects successful recharge

### Code Change
```javascript
await orderData.update({
    externalOrderId: rechargeOrder.orderId,
    providerResponse: rechargeOrder.providerResponse,
    providerMetadata: {
        iccid: parentOrder.stock.iccid,
        rechargeOrder: true
    },
    status: 'completed', // ← Added this line
    providerOrderStatus: 'ACTIVE', // ← Changed from 'PENDING'
    lastProviderCheck: new Date()
}, { transaction: t });
```

### Benefits
- ✅ Topup orders now show correct "completed" status
- ✅ Green badge instead of confusing yellow "pending" badge
- ✅ Top-up button becomes visible (allows creating more topups)
- ✅ Better user experience and reduced confusion

## Files Modified

1. `server/src/controllers/orderController.js` - Main order creation and webhook logic
2. `server/test_topup_fix.js` - Test script for F007/channel ID fix (new)
3. `server/test_topup_status_fix.js` - Test script for status fix (new)
4. `server/BILLIONCONNECT_TOPUP_FIX.md` - This documentation (new)

## Deployment Notes

- No database changes required
- No breaking changes to existing functionality
- Regular BillionConnect orders continue to work as before
- Topup orders now work correctly without conflicts
- Topup orders now display correct "completed" status immediately
