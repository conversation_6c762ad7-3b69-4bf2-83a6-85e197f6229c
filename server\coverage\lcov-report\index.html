
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.14% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>113/1581</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">1.68% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>20/1190</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.58% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/195</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.28% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>113/1551</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="controllers"><a href="controllers/index.html">controllers</a></td>
	<td data-value="11.19" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.19" class="pct low">11.19%</td>
	<td data-value="545" class="abs low">61/545</td>
	<td data-value="3.77" class="pct low">3.77%</td>
	<td data-value="371" class="abs low">14/371</td>
	<td data-value="4.87" class="pct low">4.87%</td>
	<td data-value="41" class="abs low">2/41</td>
	<td data-value="11.35" class="pct low">11.35%</td>
	<td data-value="537" class="abs low">61/537</td>
	</tr>

<tr>
	<td class="file low" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="3.98" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.98" class="pct low">3.98%</td>
	<td data-value="878" class="abs low">35/878</td>
	<td data-value="0.93" class="pct low">0.93%</td>
	<td data-value="643" class="abs low">6/643</td>
	<td data-value="3.67" class="pct low">3.67%</td>
	<td data-value="136" class="abs low">5/136</td>
	<td data-value="4.07" class="pct low">4.07%</td>
	<td data-value="858" class="abs low">35/858</td>
	</tr>

<tr>
	<td class="file low" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="10.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.75" class="pct low">10.75%</td>
	<td data-value="158" class="abs low">17/158</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="176" class="abs low">0/176</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="10.89" class="pct low">10.89%</td>
	<td data-value="156" class="abs low">17/156</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T06:37:32.421Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    