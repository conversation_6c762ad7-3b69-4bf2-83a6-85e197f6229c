const orderController = require('../../../src/controllers/orderController');
const models = require('../../../src/models');

// Mock the models
jest.mock('../../../src/models', () => ({
    Order: {
        findOne: jest.fn(),
        update: jest.fn()
    },
    EsimStock: {
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn()
    },
    EsimPlanStockHistory: {
        create: jest.fn()
    }
}));

// Mock sequelize transaction
const mockTransaction = {
    commit: jest.fn(),
    rollback: jest.fn()
};

jest.mock('../../../src/config/database', () => ({
    transaction: jest.fn(() => mockTransaction)
}));

describe('OrderController - BillionConnect Webhook', () => {
    let req, res;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Setup request and response mocks
        req = {
            body: {}
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis()
        };
    });

    describe('handleBillionConnectWebhook', () => {

        it('should process N009 webhook for regular orders', async () => {
            // Mock a regular order (not a topup)
            const mockOrder = {
                id: 'VLZ2030001',
                userId: 'user123',
                esimPlanId: 'plan123',
                status: 'pending',
                externalOrderId: 'BC_ORDER_123',
                providerMetadata: {}, // No rechargeOrder flag
                providerOrderStatus: 'PENDING',
                update: jest.fn()
            };

            models.Order.findOne.mockResolvedValue(mockOrder);

            req.body = {
                tradeType: 'N009',
                tradeTime: '2024-01-15 10:30:00',
                tradeData: {
                    orderId: 'BC_ORDER_123',
                    channelOrderId: 'VLZ2030001',
                    subOrderList: [{
                        iccid: '8988228045500123456',
                        qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                        apn: 'internet',
                        validTime: '2024-12-31 23:59:59'
                    }]
                }
            };

            await orderController.handleBillionConnectWebhook(req, res);

            // Verify that the order was found and processed
            expect(models.Order.findOne).toHaveBeenCalledWith({
                where: { externalOrderId: 'BC_ORDER_123' },
                include: [
                    { model: models.User, as: 'user' },
                    { model: models.EsimPlan, as: 'plan' }
                ],
                transaction: mockTransaction
            });

            // Verify response indicates successful processing
            expect(res.json).toHaveBeenCalledWith({
                tradeCode: '1000',
                tradeMsg: 'Webhook processed successfully'
            });
        });

        it('should skip N009 webhook processing for topup orders', async () => {
            // Mock a topup order with rechargeOrder flag
            const mockTopupOrder = {
                id: 'VLZ2030002',
                userId: 'user123',
                esimPlanId: 'plan123',
                status: 'pending',
                externalOrderId: 'BC_TOPUP_456',
                providerMetadata: {
                    iccid: '8988228045500123456',
                    rechargeOrder: true // This marks it as a topup order
                },
                providerOrderStatus: 'PENDING'
            };

            models.Order.findOne.mockResolvedValue(mockTopupOrder);

            req.body = {
                tradeType: 'N009',
                tradeTime: '2024-01-15 10:30:00',
                tradeData: {
                    orderId: 'BC_TOPUP_456',
                    channelOrderId: 'VLZ2030002',
                    subOrderList: [{
                        iccid: '8988228045500123456',
                        qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                        apn: 'internet',
                        validTime: '2024-12-31 23:59:59'
                    }]
                }
            };

            await orderController.handleBillionConnectWebhook(req, res);

            // Verify that the order was found
            expect(models.Order.findOne).toHaveBeenCalledWith({
                where: { externalOrderId: 'BC_TOPUP_456' },
                include: [
                    { model: models.User, as: 'user' },
                    { model: models.EsimPlan, as: 'plan' }
                ],
                transaction: mockTransaction
            });

            // Verify response indicates topup order was skipped
            expect(res.json).toHaveBeenCalledWith({
                tradeCode: '1000',
                tradeMsg: 'Topup order webhook acknowledged but not processed'
            });

            // Verify that no stock operations were performed
            expect(models.EsimStock.findOne).not.toHaveBeenCalled();
            expect(models.EsimStock.create).not.toHaveBeenCalled();
            expect(models.EsimPlanStockHistory.create).not.toHaveBeenCalled();
        });

        it('should ignore non-N009 webhook types', async () => {
            req.body = {
                tradeType: 'N001',
                tradeTime: '2024-01-15 10:30:00',
                tradeData: {
                    orderId: 'BC_ORDER_789',
                    channelOrderId: 'VLZ2030003'
                }
            };

            await orderController.handleBillionConnectWebhook(req, res);

            // Verify that no order lookup was performed
            expect(models.Order.findOne).not.toHaveBeenCalled();

            // Verify response indicates non-N009 webhook was ignored
            expect(res.json).toHaveBeenCalledWith({
                tradeCode: '1000',
                tradeMsg: 'Notification received but not processed'
            });
        });

        it('should handle webhook for non-existent order gracefully', async () => {
            // Mock that no order is found
            models.Order.findOne.mockResolvedValue(null);

            req.body = {
                tradeType: 'N009',
                tradeTime: '2024-01-15 10:30:00',
                tradeData: {
                    orderId: 'NON_EXISTENT_ORDER',
                    channelOrderId: 'VLZ9999999',
                    subOrderList: [{
                        iccid: '8988228045500123456',
                        qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                        apn: 'internet'
                    }]
                }
            };

            await orderController.handleBillionConnectWebhook(req, res);

            // Verify that order lookup was performed
            expect(models.Order.findOne).toHaveBeenCalledWith({
                where: { externalOrderId: 'NON_EXISTENT_ORDER' },
                include: [
                    { model: models.User, as: 'user' },
                    { model: models.EsimPlan, as: 'plan' }
                ],
                transaction: mockTransaction
            });

            // Verify response indicates order not found
            expect(res.json).toHaveBeenCalledWith({
                tradeCode: '1000',
                tradeMsg: 'Order not found but acknowledged'
            });
        });
    });
});
