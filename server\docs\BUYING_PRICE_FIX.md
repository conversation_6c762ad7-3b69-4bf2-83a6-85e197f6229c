# BillionConnect Buying Price Fix

## Issue Description

During manual syncing of BillionConnect products, the buying price was sometimes getting updated to zero. This occurred when:

1. The BillionConnect price API (F003) failed to return valid price data
2. The API returned zero or invalid settlement prices
3. Network issues prevented successful price fetching
4. Invalid price data structure was returned from the API

## Root Cause Analysis

The issue was identified in multiple locations:

### 1. `billionconnect.service.js` - `getProductsWithPrices()` method
```javascript
// Line 603 - Original problematic code
buyingPrice: priceInfo ? priceInfo.buyingPrice : 0,
```
When `priceInfo` was null or contained invalid data, the buying price was set to 0.

### 2. `billionconnect.service.js` - `transformPriceData()` method
```javascript
// Line 454 - Original problematic code
const settlementPriceCNY = parseFloat(selectedPrice.settlementPrice) || 0;
```
When settlement price was invalid, it defaulted to 0, which then got converted to $0.00 USD.

### 3. Sync Operations
Both the general sync (`performSyncOperation`) and BillionConnect-specific sync (`performBillionConnectSyncOperation`) would overwrite existing buying prices with the zero values from the API.

## Solution Implemented

### 1. Enhanced Price Validation in `transformPriceData()`
```javascript
// Validate that we have valid settlement price data
if (isNaN(settlementPriceCNY) || settlementPriceCNY <= 0) {
    console.error(`Invalid settlement price for SKU ${planPriceData.skuId}: ${selectedPrice.settlementPrice}`);
    return null; // Return null instead of zero price
}
```

### 2. Price Preservation in `getProductsWithPrices()`
```javascript
// Only update buying price if we have valid price data
let buyingPrice = product.buyingPrice || 0; // Use existing price as fallback

if (priceInfo && priceInfo.buyingPrice > 0) {
    buyingPrice = priceInfo.buyingPrice;
} else if (priceInfo && priceInfo.buyingPrice === 0) {
    console.warn(`⚠️  BillionConnect API returned zero buying price for SKU ${product.externalSkuId}. Preserving existing price: $${buyingPrice}`);
} else {
    console.warn(`⚠️  No price data available for SKU ${product.externalSkuId}. Preserving existing price: $${buyingPrice}`);
}
```

### 3. Database Update Protection in Sync Operations
```javascript
// Preserve existing buying price if the new one is zero or invalid
if (!standardizedPlan.buyingPrice || standardizedPlan.buyingPrice <= 0) {
    if (plan.buyingPrice && plan.buyingPrice > 0) {
        fieldsToPreserve.buyingPrice = plan.buyingPrice;
        console.log(`   ⚠️  Preserving existing buying price $${plan.buyingPrice} for ${plan.name} (API returned: $${standardizedPlan.buyingPrice})`);
    }
}
```

## Files Modified

1. **`server/src/services/billionconnect.service.js`**
   - Enhanced `transformPriceData()` method with price validation
   - Improved `getProductsWithPrices()` method with price preservation logic

2. **`server/src/controllers/esimPlanController.js`**
   - Updated `performSyncOperation()` to preserve buying prices
   - Updated `performBillionConnectSyncOperation()` to preserve buying prices

## Testing

A comprehensive test script was created (`server/test_buying_price_fix.js`) that verifies:

1. Invalid settlement prices return null instead of zero
2. Zero settlement prices return null instead of zero
3. Valid prices are correctly converted from CNY to USD
4. Price preservation logic works correctly
5. Warning messages are logged appropriately

## Benefits

1. **Data Integrity**: Existing valid buying prices are preserved when API fails
2. **Reliability**: Sync operations no longer corrupt price data
3. **Visibility**: Warning messages alert administrators to API issues
4. **Robustness**: System gracefully handles API failures and invalid data

## Monitoring

The fix includes comprehensive logging:
- Error messages when invalid price data is detected
- Warning messages when existing prices are preserved
- Clear indication of when API data is being used vs. preserved

## Future Improvements

1. Consider implementing a retry mechanism for failed price API calls
2. Add email notifications when price preservation occurs frequently
3. Implement a manual price override system for administrators
4. Consider caching valid prices to use as fallbacks

## Deployment Notes

This fix is backward compatible and does not require database migrations. It will take effect immediately upon deployment and will prevent future buying price corruption during sync operations.
