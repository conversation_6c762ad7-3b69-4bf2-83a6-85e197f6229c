<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner API Documentation - Professional</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/json.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <style>
        .copy-btn {
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        .endpoint-card {
            transition: all 0.3s ease;
        }
        .endpoint-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .method-badge {
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }
        .try-it-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .response-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .sidebar-nav {
            position: sticky;
            top: 2rem;
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        .endpoint-section {
            scroll-margin-top: 2rem;
        }
        .parameter-table th {
            background-color: #f8fafc;
            font-weight: 600;
        }
        .required-badge {
            background-color: #ef4444;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
        }
        .optional-badge {
            background-color: #6b7280;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
        }
        .type-badge {
            background-color: #3b82f6;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Partner API Documentation</h1>
                    <p class="mt-2 text-gray-600">Professional eSIM API for authorized partners</p>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        Version 1.0
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        OpenAPI 3.0
                    </span>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:w-1/4">
                <nav class="sidebar-nav bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Navigation</h3>
                    <ul class="space-y-2">
                        <li><a href="#overview" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">Overview</a></li>
                        <li><a href="#authentication" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">Authentication</a></li>
                        <li><a href="#endpoints" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">Endpoints</a></li>
                        <li class="ml-4">
                            <ul class="space-y-1">
                                <li><a href="#get-products" class="block px-3 py-1 text-xs text-gray-600 hover:bg-gray-50 rounded-md transition-colors">GET /products</a></li>
                                <li><a href="#create-order" class="block px-3 py-1 text-xs text-gray-600 hover:bg-gray-50 rounded-md transition-colors">POST /order</a></li>
                                <li><a href="#get-order" class="block px-3 py-1 text-xs text-gray-600 hover:bg-gray-50 rounded-md transition-colors">GET /order/{orderId}</a></li>
                                <li><a href="#get-usage" class="block px-3 py-1 text-xs text-gray-600 hover:bg-gray-50 rounded-md transition-colors">GET /usage/{orderId}</a></li>
                            </ul>
                        </li>
                        <li><a href="#error-codes" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">Error Codes</a></li>
                        <li><a href="#try-it" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">Try It</a></li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="lg:w-3/4">
                <!-- Overview Section -->
                <section id="overview" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
                        <p class="text-gray-600 mb-6">
                            The Partner API allows authorized partners to integrate eSIM services into their applications. 
                            This RESTful API provides endpoints for product discovery, order management, and usage tracking.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-blue-900 mb-2">Base URLs</h3>
                                <div class="space-y-2">
                                    <div>
                                        <span class="text-sm font-medium text-blue-700">Development:</span>
                                        <code class="block text-sm text-blue-800 bg-blue-100 px-2 py-1 rounded mt-1">http://localhost:3000/api/v1</code>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-blue-700">Production:</span>
                                        <code class="block text-sm text-blue-800 bg-blue-100 px-2 py-1 rounded mt-1">https://api.vizlync.net/api/v1</code>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-green-900 mb-2">Key Features</h3>
                                <ul class="text-sm text-green-800 space-y-1">
                                    <li>• Real-time product catalog</li>
                                    <li>• Instant order processing</li>
                                    <li>• Usage data tracking</li>
                                    <li>• Comprehensive error handling</li>
                                    <li>• Rate limiting protection</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Authentication Section -->
                <section id="authentication" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Authentication</h2>
                        
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        All API requests require authentication. Include the required headers with every request.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Required Headers</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Header</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">Authorization</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="required-badge">Required</span></td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Bearer token with your API key</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">X-Partner-ID</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="required-badge">Required</span></td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Your unique partner identifier</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">Example Authentication</h4>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('auth-example')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="auth-example" class="language-bash">curl -X GET "http://localhost:3000/api/v1/products" \
  -H "Authorization: Bearer your-api-key-here" \
  -H "X-Partner-ID: your-partner-id-here" \
  -H "Content-Type: application/json"</code></pre>
                            </div>
                        </div>

                        <!-- API Key Configuration -->
                        <div class="mt-8 bg-gray-50 p-6 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">API Configuration</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="api-key-input" class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                    <input type="password" id="api-key-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter your API key">
                                </div>
                                <div>
                                    <label for="partner-id-input" class="block text-sm font-medium text-gray-700 mb-2">Partner ID</label>
                                    <input type="text" id="partner-id-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter your Partner ID">
                                </div>
                            </div>
                            <div class="mt-4">
                                <label for="base-url-select" class="block text-sm font-medium text-gray-700 mb-2">Environment</label>
                                <select id="base-url-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="http://localhost:3000/api/v1">Development (localhost:3000)</option>
                                    <option value="https://api.vizlync.net/api/v1">Production (api.vizlync.net)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Endpoints Section -->
                <section id="endpoints" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>

                        <!-- Endpoints Overview -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                            <div class="endpoint-card bg-green-50 border border-green-200 rounded-lg p-4 cursor-pointer" onclick="scrollToSection('get-products')">
                                <div class="flex items-center justify-between">
                                    <span class="method-badge bg-green-500 text-white">GET</span>
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                                <h3 class="text-sm font-semibold text-gray-900 mt-2">/products</h3>
                                <p class="text-xs text-gray-600 mt-1">Get all available products</p>
                            </div>

                            <div class="endpoint-card bg-blue-50 border border-blue-200 rounded-lg p-4 cursor-pointer" onclick="scrollToSection('create-order')">
                                <div class="flex items-center justify-between">
                                    <span class="method-badge bg-blue-500 text-white">POST</span>
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                                <h3 class="text-sm font-semibold text-gray-900 mt-2">/order</h3>
                                <p class="text-xs text-gray-600 mt-1">Create a new order</p>
                            </div>

                            <div class="endpoint-card bg-purple-50 border border-purple-200 rounded-lg p-4 cursor-pointer" onclick="scrollToSection('get-order')">
                                <div class="flex items-center justify-between">
                                    <span class="method-badge bg-purple-500 text-white">GET</span>
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                                <h3 class="text-sm font-semibold text-gray-900 mt-2">/order/{orderId}</h3>
                                <p class="text-xs text-gray-600 mt-1">Get order details</p>
                            </div>

                            <div class="endpoint-card bg-orange-50 border border-orange-200 rounded-lg p-4 cursor-pointer" onclick="scrollToSection('get-usage')">
                                <div class="flex items-center justify-between">
                                    <span class="method-badge bg-orange-500 text-white">GET</span>
                                    <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                                <h3 class="text-sm font-semibold text-gray-900 mt-2">/usage/{orderId}</h3>
                                <p class="text-xs text-gray-600 mt-1">Get usage information</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- GET /products -->
                <section id="get-products" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <div class="flex items-center mb-6">
                            <span class="method-badge bg-green-500 text-white mr-4">GET</span>
                            <h2 class="text-2xl font-bold text-gray-900">/products</h2>
                        </div>

                        <p class="text-gray-600 mb-6">
                            Returns a list of all available eSIM products. The countries field contains the specific countries
                            supported by each individual plan, not all countries in the region.
                        </p>

                        <!-- Query Parameters -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Query Parameters</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">region</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="optional-badge">Optional</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">Filter products by region (e.g., "Europe", "Asia")</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">country</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="optional-badge">Optional</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">Filter products by country code (e.g., "US", "GB")</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- cURL Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">cURL Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-products-curl')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="get-products-curl" class="language-bash">curl -X GET "http://localhost:3000/api/v1/products" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json"</code></pre>
                            </div>
                        </div>

                        <!-- Response Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Response Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-products-response')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto response-container"><code id="get-products-response" class="language-json">{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "FXTEND132075",
        "name": "Oceania 3 GB",
        "description": "Key Features:\n• This is a data-only eSIM...",
        "planInfo": "HTML formatted plan information",
        "instructions": null,
        "price": 29.99,
        "validityDays": 7,
        "countries": ["FJ", "PG", "WS", "TO"],
        "region": ["Oceania"],
        "dataAmount": 3,
        "dataUnit": "GB",
        "customPlanData": null,
        "voiceMin": null,
        "voiceMinUnit": null,
        "speed": "Unrestricted",
        "planType": "Fixed",
        "category": "esim_realtime",
        "networkType": "4G/LTE",
        "isVoiceAvailable": false,
        "isSmsAvailable": false,
        "hotspotAvailable": true,
        "topUpAvailable": false,
        "profile": "local",
        "activationPolicy": "Activation upon purchase",
        "startDateEnabled": false,
        "features": []
      }
    ],
    "total": 18663
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Try It Section -->
                        <div class="try-it-container rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-4">Try It</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Region (Optional)</label>
                                    <input type="text" id="products-region" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900" placeholder="e.g., Europe">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Country (Optional)</label>
                                    <input type="text" id="products-country" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900" placeholder="e.g., US">
                                </div>
                            </div>
                            <button onclick="tryGetProducts()" class="bg-white text-purple-600 px-6 py-2 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                                Send Request
                            </button>
                            <div id="products-response" class="mt-4 bg-gray-900 text-gray-100 p-4 rounded-lg hidden">
                                <pre><code class="language-json"></code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- POST /order -->
                <section id="create-order" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <div class="flex items-center mb-6">
                            <span class="method-badge bg-blue-500 text-white mr-4">POST</span>
                            <h2 class="text-2xl font-bold text-gray-900">/order</h2>
                        </div>

                        <p class="text-gray-600 mb-6">
                            Places a new order for an eSIM product. Returns essential order information only.
                            Use the Order Details endpoint with the returned orderId to retrieve complete order information.
                        </p>

                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">
                                        <strong>Note:</strong> API orders trigger email notifications to both partner and admin with '(Via API)' in the subject line.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Request Body Parameters -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Request Body Parameters</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">productId</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="required-badge">Required</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">The ID of the product to order</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">startDate</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="optional-badge">Optional</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">Start date for the eSIM (YYYY-MM-DD format)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- cURL Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">cURL Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('create-order-curl')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="create-order-curl" class="language-bash">curl -X POST "http://localhost:3000/api/v1/order" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "FXTEND132075",
    "startDate": "2024-01-15"
  }'</code></pre>
                            </div>
                        </div>

                        <!-- Response Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Response Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('create-order-response')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto response-container"><code id="create-order-response" class="language-json">{
  "success": true,
  "orderId": "VLZ123456",
  "message": "Order created successfully"
}</code></pre>
                            </div>
                        </div>

                        <!-- Try It Section -->
                        <div class="try-it-container rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-4">Try It</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Product ID <span class="text-red-300">*</span></label>
                                    <input type="text" id="order-product-id" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900" placeholder="e.g., FXTEND132075" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Start Date (Optional)</label>
                                    <input type="date" id="order-start-date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900">
                                </div>
                            </div>
                            <button onclick="tryCreateOrder()" class="bg-white text-purple-600 px-6 py-2 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                                Send Request
                            </button>
                            <div id="order-response" class="mt-4 bg-gray-900 text-gray-100 p-4 rounded-lg hidden">
                                <pre><code class="language-json"></code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- GET /order/{orderId} -->
                <section id="get-order" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <div class="flex items-center mb-6">
                            <span class="method-badge bg-purple-500 text-white mr-4">GET</span>
                            <h2 class="text-2xl font-bold text-gray-900">/order/{orderId}</h2>
                        </div>

                        <p class="text-gray-600 mb-6">
                            Retrieve detailed order information using the order ID. This endpoint provides complete order details
                            including eSIM data, pricing, and fulfillment information.
                        </p>

                        <!-- Path Parameters -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Path Parameters</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">orderId</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="required-badge">Required</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">The unique identifier of the order</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- cURL Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">cURL Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-order-curl')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="get-order-curl" class="language-bash">curl -X GET "http://localhost:3000/api/v1/order/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json"</code></pre>
                            </div>
                        </div>

                        <!-- Response Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Response Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-order-response')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto response-container"><code id="get-order-response" class="language-json">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "product": {
      "productId": "FXTEND132075",
      "name": "Oceania 3 GB"
    },
    "orderTotal": 29.99,
    "quantity": 1,
    "startDate": "2024-01-15",
    "expiryDate": "2024-01-22",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "walletAuthTransactionId": "sksgdnsdyk1234567890",
    "createdAt": "2024-01-10T14:30:45Z"
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Try It Section -->
                        <div class="try-it-container rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-4">Try It</h3>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2">Order ID <span class="text-red-300">*</span></label>
                                <input type="text" id="get-order-id" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900" placeholder="e.g., VLZ123456" required>
                            </div>
                            <button onclick="tryGetOrder()" class="bg-white text-purple-600 px-6 py-2 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                                Send Request
                            </button>
                            <div id="get-order-response-container" class="mt-4 bg-gray-900 text-gray-100 p-4 rounded-lg hidden">
                                <pre><code class="language-json"></code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- GET /usage/{orderId} -->
                <section id="get-usage" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <div class="flex items-center mb-6">
                            <span class="method-badge bg-orange-500 text-white mr-4">GET</span>
                            <h2 class="text-2xl font-bold text-gray-900">/usage/{orderId}</h2>
                        </div>

                        <p class="text-gray-600 mb-6">
                            Returns usage details for a specific eSIM order. Provides real-time data usage information
                            when available from the provider.
                        </p>

                        <div class="bg-orange-50 border-l-4 border-orange-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-orange-700">
                                        <strong>Note:</strong> Usage data availability depends on the provider. Some providers may require manual refresh to show updated usage data.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Path Parameters -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Path Parameters</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">orderId</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="type-badge">string</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="required-badge">Required</span></td>
                                            <td class="px-6 py-4 text-sm text-gray-500">The unique identifier of the order</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- cURL Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">cURL Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-usage-curl')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="get-usage-curl" class="language-bash">curl -X GET "http://localhost:3000/api/v1/usage/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json"</code></pre>
                            </div>
                        </div>

                        <!-- Response Example -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Response Example</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('get-usage-response')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto response-container"><code id="get-usage-response" class="language-json">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "dataUsage": 2147483648,
    "dataAllowance": 5368709120,
    "status": "Active",
    "expiryDate": "2024-01-22T00:00:00Z",
    "lastUpdated": "2024-01-15T09:45:22Z",
    "message": "Usage data retrieved successfully",
    "isRealtime": true,
    "fromCache": false
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Try It Section -->
                        <div class="try-it-container rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-4">Try It</h3>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2">Order ID <span class="text-red-300">*</span></label>
                                <input type="text" id="usage-order-id" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900" placeholder="e.g., VLZ123456" required>
                            </div>
                            <button onclick="tryGetUsage()" class="bg-white text-purple-600 px-6 py-2 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                                Send Request
                            </button>
                            <div id="usage-response" class="mt-4 bg-gray-900 text-gray-100 p-4 rounded-lg hidden">
                                <pre><code class="language-json"></code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Error Codes Section -->
                <section id="error-codes" class="endpoint-section mb-12">
                    <div class="bg-white rounded-lg shadow-sm p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Error Codes</h2>

                        <p class="text-gray-600 mb-6">
                            The API uses conventional HTTP response codes to indicate the success or failure of an API request.
                        </p>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 parameter-table">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HTTP Code</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error Code</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">200</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SUCCESS</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Request completed successfully</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">201</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CREATED</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Resource created successfully</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">400</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">INVALID_REQUEST</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">The request was invalid or cannot be served</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">401</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">UNAUTHORIZED</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Invalid API key or partner ID</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">404</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NOT_FOUND</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">The requested resource was not found</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">429</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RATE_LIMIT_EXCEEDED</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Too many requests, rate limit exceeded</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">500</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">INTERNAL_SERVER_ERROR</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Something went wrong on our end</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Error Response Format</h3>
                            <div class="relative">
                                <button class="copy-btn absolute top-2 right-2 bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700" onclick="copyToClipboard('error-response-example')">
                                    Copy
                                </button>
                                <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"><code id="error-response-example" class="language-json">{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Product ID is required"
  }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center text-gray-500">
                <p>&copy; 2025 eSIM Platform. All rights reserved.</p>
                <p class="mt-2 text-sm">Professional API Documentation - Version 1.0</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize highlight.js
        hljs.highlightAll();

        // Smooth scrolling for navigation
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Copy to clipboard functionality
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                const button = element.parentElement.querySelector('.copy-btn');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('bg-green-600');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-600');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
            });
        }

        // Get API credentials from form
        function getApiCredentials() {
            const apiKey = document.getElementById('api-key-input').value;
            const partnerId = document.getElementById('partner-id-input').value;
            const baseUrl = document.getElementById('base-url-select').value;

            if (!apiKey || !partnerId) {
                alert('Please enter your API Key and Partner ID in the Authentication section.');
                return null;
            }

            return { apiKey, partnerId, baseUrl };
        }

        // Show response in container
        function showResponse(containerId, data, status = 200) {
            const container = document.getElementById(containerId);
            const codeElement = container.querySelector('code');

            container.classList.remove('hidden');
            codeElement.textContent = JSON.stringify(data, null, 2);
            hljs.highlightElement(codeElement);

            // Add status indicator
            let statusElement = container.querySelector('.status-indicator');
            if (!statusElement) {
                statusElement = document.createElement('div');
                statusElement.className = 'status-indicator text-sm mb-2';
                container.insertBefore(statusElement, container.firstChild);
            }

            const isSuccess = status >= 200 && status < 300;
            statusElement.className = `status-indicator text-sm mb-2 ${isSuccess ? 'text-green-400' : 'text-red-400'}`;
            statusElement.textContent = `Status: ${status}`;
        }

        // Try GET /products
        async function tryGetProducts() {
            const credentials = getApiCredentials();
            if (!credentials) return;

            const region = document.getElementById('products-region').value;
            const country = document.getElementById('products-country').value;

            let url = `${credentials.baseUrl}/products`;
            const params = new URLSearchParams();
            if (region) params.append('region', region);
            if (country) params.append('country', country);
            if (params.toString()) url += `?${params.toString()}`;

            try {
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${credentials.apiKey}`,
                        'X-Partner-ID': credentials.partnerId,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                showResponse('products-response', data, response.status);
            } catch (error) {
                showResponse('products-response', {
                    success: false,
                    error: {
                        code: 'NETWORK_ERROR',
                        message: error.message
                    }
                }, 0);
            }
        }

        // Try POST /order
        async function tryCreateOrder() {
            const credentials = getApiCredentials();
            if (!credentials) return;

            const productId = document.getElementById('order-product-id').value;
            const startDate = document.getElementById('order-start-date').value;

            if (!productId) {
                alert('Product ID is required');
                return;
            }

            const requestBody = { productId };
            if (startDate) requestBody.startDate = startDate;

            try {
                const response = await fetch(`${credentials.baseUrl}/order`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${credentials.apiKey}`,
                        'X-Partner-ID': credentials.partnerId,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                showResponse('order-response', data, response.status);
            } catch (error) {
                showResponse('order-response', {
                    success: false,
                    error: {
                        code: 'NETWORK_ERROR',
                        message: error.message
                    }
                }, 0);
            }
        }

        // Try GET /order/{orderId}
        async function tryGetOrder() {
            const credentials = getApiCredentials();
            if (!credentials) return;

            const orderId = document.getElementById('get-order-id').value;

            if (!orderId) {
                alert('Order ID is required');
                return;
            }

            try {
                const response = await fetch(`${credentials.baseUrl}/order/${orderId}`, {
                    headers: {
                        'Authorization': `Bearer ${credentials.apiKey}`,
                        'X-Partner-ID': credentials.partnerId,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                showResponse('get-order-response-container', data, response.status);
            } catch (error) {
                showResponse('get-order-response-container', {
                    success: false,
                    error: {
                        code: 'NETWORK_ERROR',
                        message: error.message
                    }
                }, 0);
            }
        }

        // Try GET /usage/{orderId}
        async function tryGetUsage() {
            const credentials = getApiCredentials();
            if (!credentials) return;

            const orderId = document.getElementById('usage-order-id').value;

            if (!orderId) {
                alert('Order ID is required');
                return;
            }

            try {
                const response = await fetch(`${credentials.baseUrl}/usage/${orderId}`, {
                    headers: {
                        'Authorization': `Bearer ${credentials.apiKey}`,
                        'X-Partner-ID': credentials.partnerId,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                showResponse('usage-response', data, response.status);
            } catch (error) {
                showResponse('usage-response', {
                    success: false,
                    error: {
                        code: 'NETWORK_ERROR',
                        message: error.message
                    }
                }, 0);
            }
        }

        // Smooth scrolling for sidebar navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('nav a[href^="#"]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    scrollToSection(targetId);
                });
            });

            // Highlight current section in navigation
            const sections = document.querySelectorAll('.endpoint-section');
            const navItems = document.querySelectorAll('nav a[href^="#"]');

            function highlightCurrentSection() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                navItems.forEach(item => {
                    item.classList.remove('bg-blue-100', 'text-blue-700');
                    if (item.getAttribute('href') === '#' + current) {
                        item.classList.add('bg-blue-100', 'text-blue-700');
                    }
                });
            }

            window.addEventListener('scroll', highlightCurrentSection);
            highlightCurrentSection(); // Initial call
        });
    </script>
</body>
</html>
