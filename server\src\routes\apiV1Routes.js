const express = require('express');
const router = express.Router();
const apiV1Controller = require('../controllers/apiV1Controller');
const { authenticateApiKey, apiLimiter } = require('../middleware/apiAuth');

// Apply API key authentication and rate limiting to all routes
router.use(authenticateApiKey);
router.use(apiLimiter);

// Product routes
router.get('/products', apiV1Controller.getProducts);

// Order routes
router.post('/order', apiV1Controller.createOrder);
router.get('/order/:orderId', apiV1Controller.getOrderDetails);

// Usage routes
router.get('/usage/:orderId', apiV1Controller.getUsageInfo);

module.exports = router; 